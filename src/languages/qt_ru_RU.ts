<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="ru_RU">
<context>
    <name></name>
    <message>
        <source>Virtual Window </source>
        <translation type="obsolete">Виртуальное окно </translation>
    </message>
    <message>
        <source>View </source>
        <translation type="obsolete">Вид </translation>
    </message>
</context>
<context>
    <name>AICameraZoneWidget</name>
    <message>
        <source>Detection Zone</source>
        <translation type="obsolete">Зона обнаружения</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="69"/>
        <source>Entry Counting Zone</source>
        <translation type="unfinished">Зона подсчёта входа</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="70"/>
        <source>Exit Counting Zone</source>
        <translation type="unfinished">Зона подсчёта выхода</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="77"/>
        <source>Intrusion Zone</source>
        <translation type="unfinished">Зона вторжения</translation>
    </message>
    <message>
        <source>Access Control Entry Zone</source>
        <translation type="obsolete">Зона входа контроля доступа</translation>
    </message>
    <message>
        <source>Access Control Exit Zone</source>
        <translation type="obsolete">Зона выхода контроля доступа</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="117"/>
        <source>Note: Choose 4 points to form a Quadrilateral.</source>
        <translation type="unfinished">Примечание: Выберите 4 точки, чтобы сформировать четырехугольник.</translation>
    </message>
    <message>
        <source>Zone type</source>
        <translation type="obsolete">Тип зоны</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="1111"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="1239"/>
        <source>Saved Zone List</source>
        <translation type="unfinished">Список сохранённых зон</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="144"/>
        <source>Enter zone name</source>
        <translation type="unfinished">Введите имя зоны</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="78"/>
        <source>Recognition Zone</source>
        <translation type="unfinished">Зона распознавания</translation>
    </message>
    <message>
        <source>Select device</source>
        <translation type="obsolete">Выберите устройство</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="154"/>
        <source>Draw</source>
        <translation type="unfinished">Нарисовать</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="158"/>
        <source>Clear</source>
        <translation type="unfinished">Очистить</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="162"/>
        <source>Save</source>
        <translation type="unfinished">Сохранить</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="476"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="791"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="1057"/>
        <source>Human</source>
        <translation type="unfinished">Человек</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="478"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="792"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="1060"/>
        <source>Vehicle</source>
        <translation type="unfinished">Транспортное средство</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="81"/>
        <source>Protection Zone</source>
        <translation type="unfinished">Зона защиты</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="977"/>
        <source>Edit Zone</source>
        <translation type="unfinished">Редактировать зону</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="987"/>
        <source>Zone Name:</source>
        <translation type="unfinished">Имя зоны:</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="993"/>
        <source>Device Access Control:</source>
        <translation type="unfinished">Управление доступом к устройству:</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="1170"/>
        <source>Zone Type</source>
        <translation type="unfinished">Тип зоны</translation>
    </message>
    <message>
        <source>Drawing Type</source>
        <translation type="obsolete">Тип рисования</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="1188"/>
        <source>Zone Name</source>
        <translation type="unfinished">Имя зоны</translation>
    </message>
    <message>
        <source>Access Control Device</source>
        <translation type="obsolete">Устройство управления доступом</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="1176"/>
        <source>Recognition Type</source>
        <translation type="unfinished">Тип распознавания</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="323"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="1137"/>
        <source>All</source>
        <translation type="unfinished">Все</translation>
    </message>
    <message>
        <source>Polygon</source>
        <translation type="obsolete">Многоугольник</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="71"/>
        <source>Entry Protection Zone</source>
        <translation type="unfinished">Зона защиты входа</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="72"/>
        <source>Exit Protection Zone</source>
        <translation type="unfinished">Зона защиты выхода</translation>
    </message>
    <message>
        <source>Entry Access Zone</source>
        <translation type="obsolete">Зона управления доступом входа</translation>
    </message>
    <message>
        <source>Exit Access Zone</source>
        <translation type="obsolete">Зона управления доступом выхода</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="425"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="539"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="544"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="549"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="639"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="644"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="649"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="655"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="660"/>
        <source>Warning</source>
        <translation type="unfinished">Предупреждение</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="425"/>
        <source>Failed to delete the zone</source>
        <translation type="unfinished">Не удалось удалить зону</translation>
    </message>
    <message>
        <source>Note: Choose 2 points to form a Line.</source>
        <translation type="obsolete">Примечание: Выберите 2 точки, чтобы сформировать линию.</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="540"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="640"/>
        <source>Please choose Zone type</source>
        <translation type="unfinished">Пожалуйста, выберите тип зоны</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="545"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="645"/>
        <source>Zone name is empty</source>
        <translation type="unfinished">Имя зоны пустое</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="550"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="650"/>
        <source>Zone name is exist</source>
        <translation type="unfinished">Имя зоны уже существует</translation>
    </message>
    <message>
        <source>IP Address is empty</source>
        <translation type="obsolete">IP-адрес пустой</translation>
    </message>
    <message>
        <source>Port is empty</source>
        <translation type="obsolete">Порт пустой</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="656"/>
        <source>Please choose 4 points to form a Quadrilateral.</source>
        <translation type="unfinished">Пожалуйста, Выберите 4 точки, чтобы сформировать четырехугольник.</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="661"/>
        <source>Please click on one of the four edges to determine the direction.</source>
        <translation type="unfinished">Пожалуйста, щелкните на одном из четырех краев, чтобы определить направление.</translation>
    </message>
</context>
<context>
    <name>AIConfigurationWidget</name>
    <message>
        <source>Add Script</source>
        <translation type="obsolete">Добавить скрипт</translation>
    </message>
</context>
<context>
    <name>AIScriptItem</name>
    <message>
        <source>AI Script: </source>
        <translation type="obsolete">Скрипт ИИ: </translation>
    </message>
    <message>
        <source>AI Type: </source>
        <translation type="obsolete">Тип ИИ: </translation>
    </message>
    <message>
        <source>AI Problem: </source>
        <translation type="obsolete">Проблема ИИ: </translation>
    </message>
    <message>
        <source>Zone: </source>
        <translation type="obsolete">Зона: </translation>
    </message>
    <message>
        <source>Human</source>
        <translation type="obsolete">Человек</translation>
    </message>
    <message>
        <source>Vehicle</source>
        <translation type="obsolete">Транспортное средство</translation>
    </message>
    <message>
        <source>Crowd</source>
        <translation type="obsolete">Толпа</translation>
    </message>
    <message>
        <source>Feature</source>
        <translation type="obsolete">Функция</translation>
    </message>
    <message>
        <source>Intrusion</source>
        <translation type="obsolete">Вторжение</translation>
    </message>
    <message>
        <source>Flow</source>
        <translation type="obsolete">Поток</translation>
    </message>
    <message>
        <source>Access Control</source>
        <translation type="obsolete">Контроль</translation>
    </message>
</context>
<context>
    <name>AIZoneDropDownDraw</name>
    <message>
        <source>Polygon</source>
        <translation type="obsolete">Многоугольник</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_zone_dropdown_draw.py" line="56"/>
        <location filename="../presentation/device_management_screen/widget/ai_zone_dropdown_draw.py" line="58"/>
        <source>Select Items</source>
        <translation type="unfinished">Выберите элементы</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_zone_dropdown_draw.py" line="57"/>
        <source>All Items Selected</source>
        <translation type="unfinished">Все элементы выбраны</translation>
    </message>
</context>
<context>
    <name>AddCameraDialog</name>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="45"/>
        <source>ADD CAMERAS</source>
        <translation type="unfinished">ДОБАВИТЬ КАМЕРЫ</translation>
    </message>
    <message>
        <source>Server</source>
        <translation type="obsolete">Сервер</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="91"/>
        <source>Known Address</source>
        <translation type="unfinished">Известный адрес</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="95"/>
        <source>Subnet Scan</source>
        <translation type="unfinished">Сканирование подсети</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="122"/>
        <source>Address</source>
        <translation type="unfinished">Адрес</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="122"/>
        <source>IP/Hostname/RTSP/UDP Link</source>
        <translation type="unfinished">IP/Имя хоста/RTSP/UDP ссылка</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="126"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="353"/>
        <source>Port</source>
        <translation type="unfinished">Порт</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="132"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="359"/>
        <source>Default</source>
        <translation type="unfinished">По умолчанию</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="150"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="378"/>
        <source>Login</source>
        <translation type="unfinished">Войти</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="150"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="378"/>
        <source>Username</source>
        <translation type="unfinished">Имя пользователя</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="151"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="379"/>
        <source>Password</source>
        <translation type="unfinished">Пароль</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="156"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="656"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="721"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="966"/>
        <source>Search</source>
        <translation type="unfinished">Поиск</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="200"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="423"/>
        <source>Please enter all required information</source>
        <translation type="unfinished">Пожалуйста, введите всю необходимую информацию</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="214"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="441"/>
        <source>BRAND</source>
        <translation type="unfinished">БРЕНД</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="214"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="441"/>
        <source>MODEL</source>
        <translation type="unfinished">Модель</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="214"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="441"/>
        <source>ADDRESS</source>
        <translation type="unfinished">АДРЕС</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="215"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="442"/>
        <source>STATUS</source>
        <translation type="unfinished">СТАТУС</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="271"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="497"/>
        <source>Add to Groups:</source>
        <translation type="unfinished">Добавить в группы:</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="277"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="502"/>
        <source>Select group</source>
        <translation type="unfinished">Выберите группу</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="286"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="512"/>
        <source>Please choose a group to add cameras.</source>
        <translation type="unfinished">Пожалуйста, выберите группу для добавления камер.</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="313"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="539"/>
        <source>No Data</source>
        <translation type="unfinished">Нет данных</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="322"/>
        <source>Searching</source>
        <translation type="unfinished">Поиск</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="346"/>
        <source>From IP</source>
        <translation type="unfinished">С IP-адреса</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="346"/>
        <source>0.0.0.0</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="347"/>
        <source>To IP</source>
        <translation type="unfinished">На IP-адрес</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="347"/>
        <source>*********</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="123"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="350"/>
        <source>Onvif Port</source>
        <translation type="unfinished">Порт Onvif</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="390"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="730"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="778"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="994"/>
        <source>Scan</source>
        <translation type="unfinished">Сканировать</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="547"/>
        <source>Scanning</source>
        <translation type="unfinished">Сканирование</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="670"/>
        <source>Please enter correct IP or RTSP format</source>
        <translation type="unfinished">Пожалуйста, введите правильный IP-адрес или формат RTSP.</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="709"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="766"/>
        <source>Stop</source>
        <translation type="unfinished">Остановить</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="946"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="948"/>
        <source>Add</source>
        <translation type="unfinished">Добавить</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="946"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="948"/>
        <source>Devices to Group:</source>
        <translation type="unfinished">Устройства в группу:</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="983"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="1008"/>
        <source>Devices</source>
        <translation type="unfinished">Устройства</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="984"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="1009"/>
        <source>Add 0 Devices to Group:</source>
        <translation type="unfinished">Добавить 0 устройств в группу:</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="1088"/>
        <source>Added</source>
        <translation type="unfinished">Добавлено</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="1091"/>
        <source>New</source>
        <translation type="unfinished">Новый</translation>
    </message>
</context>
<context>
    <name>AddCameraGroupDialog</name>
    <message>
        <source>ADD CAMERA GROUPS</source>
        <translation type="obsolete">ДОБАВИТЬ ГРУППЫ КАМЕР</translation>
    </message>
    <message>
        <source>Group Name (*)</source>
        <translation type="obsolete">Имя группы (*)</translation>
    </message>
    <message>
        <source>Enter group name</source>
        <translation type="obsolete">Введите имя группы</translation>
    </message>
    <message>
        <source>Choose camera</source>
        <translation type="obsolete">Выберите камеру</translation>
    </message>
    <message>
        <source>Please Enter Complete Information</source>
        <translation type="obsolete">Пожалуйста, введите полную информацию</translation>
    </message>
</context>
<context>
    <name>AddCameraManual</name>
    <message>
        <source>Processing...</source>
        <translation type="obsolete">Обработка...</translation>
    </message>
    <message>
        <source>Cancel</source>
        <translation type="obsolete">Отмена</translation>
    </message>
    <message>
        <source>Description</source>
        <translation type="obsolete">Описание</translation>
    </message>
    <message>
        <source>Create</source>
        <translation type="obsolete">Создать</translation>
    </message>
    <message>
        <source>Latitude</source>
        <translation type="obsolete">Широта</translation>
    </message>
    <message>
        <source>Longitude</source>
        <translation type="obsolete">Долгота</translation>
    </message>
    <message>
        <source>Connect</source>
        <translation type="obsolete">Подключить</translation>
    </message>
    <message>
        <source>Please Enter Complete Information</source>
        <translation type="obsolete">Пожалуйста, введите полную информацию</translation>
    </message>
</context>
<context>
    <name>AddFloorDialog</name>
    <message>
        <location filename="../common/widget/dialogs/add_floor_dialog.py" line="24"/>
        <source>Building Name</source>
        <translation type="unfinished">Назвать здание</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_floor_dialog.py" line="30"/>
        <location filename="../common/widget/dialogs/add_floor_dialog.py" line="31"/>
        <source>Floor Name</source>
        <translation type="unfinished">Название Этажa</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_floor_dialog.py" line="34"/>
        <source>Upload image (File png, jpg, jpeg)</source>
        <translation type="unfinished">Загрузить изображение (Файл png, jpg, jpeg)</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_floor_dialog.py" line="46"/>
        <source>Level</source>
        <translation type="unfinished">Этаж</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_floor_dialog.py" line="54"/>
        <source>Image</source>
        <translation type="unfinished">Изображение</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_floor_dialog.py" line="67"/>
        <source>Floor name cannot be empty</source>
        <translation type="unfinished">Название этажа не может быть пустым</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_floor_dialog.py" line="73"/>
        <source>Floor image cannot be empty</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>AddGroupDialog</name>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="540"/>
        <source>Group Name</source>
        <translation type="unfinished">Имя группы</translation>
    </message>
    <message>
        <source>Status</source>
        <translation type="obsolete">Статус</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="560"/>
        <source>Selected:</source>
        <translation type="unfinished">выбранный:</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="572"/>
        <source>Search</source>
        <translation type="unfinished">Поиск</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="577"/>
        <source>All</source>
        <translation type="unfinished">Все</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="578"/>
        <source>Camera</source>
        <translation type="unfinished">Камера</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="579"/>
        <source>AIBox</source>
        <translation type="unfinished">AIBox</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="580"/>
        <source>Door</source>
        <translation type="unfinished">дверь</translation>
    </message>
    <message>
        <source>Device Type</source>
        <translation type="obsolete">Тип устройства</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="611"/>
        <source>No Data</source>
        <translation type="unfinished">Нет данных</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="619"/>
        <source>Searching</source>
        <translation type="unfinished">Поиск</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="813"/>
        <source>Group name is required</source>
        <translation type="unfinished">Требуется имя группы.</translation>
    </message>
</context>
<context>
    <name>AddMapDialog</name>
    <message>
        <source>Image</source>
        <translation type="obsolete">Изображение</translation>
    </message>
    <message>
        <source>Add Map</source>
        <translation type="obsolete">Добавить карту</translation>
    </message>
    <message>
        <source>Cancel</source>
        <translation type="obsolete">Отмена</translation>
    </message>
</context>
<context>
    <name>AddRoleDialog</name>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="645"/>
        <source>ADD NEW ROLE</source>
        <translation type="unfinished">ДОБАВИТЬ НОВУЮ ГРУППУ</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="681"/>
        <source>System Permissions</source>
        <translation type="unfinished">Системные разрешения</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="683"/>
        <source>List Of Users</source>
        <translation type="unfinished">Список пользователей</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="720"/>
        <source>Please enter the group name.</source>
        <translation type="unfinished">Пожалуйста, введите название группы.</translation>
    </message>
</context>
<context>
    <name>AddServerWidget</name>
    <message>
        <location filename="../presentation/server_screen/add_server_widget.py" line="28"/>
        <source>Connected</source>
        <translation type="unfinished">Подключено</translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/add_server_widget.py" line="30"/>
        <source>Disconnected</source>
        <translation type="unfinished">Отключено</translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/add_server_widget.py" line="46"/>
        <location filename="../presentation/server_screen/add_server_widget.py" line="47"/>
        <location filename="../presentation/server_screen/add_server_widget.py" line="74"/>
        <source>Add Server</source>
        <translation type="unfinished">Добавить сервер</translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/add_server_widget.py" line="48"/>
        <source>Search Server</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/add_server_widget.py" line="49"/>
        <location filename="../presentation/server_screen/add_server_widget.py" line="122"/>
        <source>Search</source>
        <translation type="unfinished">Поиск</translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/add_server_widget.py" line="50"/>
        <location filename="../presentation/server_screen/add_server_widget.py" line="124"/>
        <source>Cancel</source>
        <translation type="unfinished">Отмена</translation>
    </message>
</context>
<context>
    <name>AddUserDialog</name>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="41"/>
        <source>ADD NEW USER</source>
        <translation type="unfinished">ДОБАВИТЬ НОВОГО ПОЛЬЗОВАТЕЛЯ</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="63"/>
        <source>Avatar</source>
        <translation type="unfinished">Аватар</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="76"/>
        <source>Username</source>
        <translation type="unfinished">Имя пользователя</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="77"/>
        <source>Full Name</source>
        <translation type="unfinished">Полное имя</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="78"/>
        <source>System User Group</source>
        <translation type="unfinished">Группа пользователей системы</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="79"/>
        <source>Enter Username</source>
        <translation type="unfinished">Введите имя пользователя</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="81"/>
        <source>Enter Full Name</source>
        <translation type="unfinished">Введите полное имя</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="86"/>
        <source>Select Group</source>
        <translation type="unfinished">Выберите группу</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="100"/>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="104"/>
        <source>Subsystem</source>
        <translation type="unfinished">Подсистема</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="101"/>
        <source>Password</source>
        <translation type="unfinished">Пароль</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="102"/>
        <source>Status</source>
        <translation type="unfinished">Статус</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="104"/>
        <source>The password must be a minimum of 6 characters and a maximum of 32 characters, including uppercase and lowercase letters, numbers, and special characters.</source>
        <translation type="unfinished">Пароль должен содержать минимум 6 символов и максимум 32 символа, включая прописные и строчные буквы, цифры и специальные символы.</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="108"/>
        <source>Enter Password</source>
        <translation type="unfinished">Введите пароль</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="114"/>
        <source>Active</source>
        <translation type="unfinished">Активный</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="114"/>
        <source>In-active</source>
        <translation type="unfinished">Неактивный</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="127"/>
        <source>Phone number</source>
        <translation type="unfinished">Номер телефона</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="128"/>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="132"/>
        <source>Position</source>
        <translation type="unfinished">Должность</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="129"/>
        <source>Gender</source>
        <translation type="unfinished">Пол</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="130"/>
        <source>Phone Number</source>
        <translation type="unfinished">Номер телефона</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="135"/>
        <source>Male</source>
        <translation type="unfinished">Мужской</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="135"/>
        <source>Female</source>
        <translation type="unfinished">Женский</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="147"/>
        <source>Email</source>
        <translation type="unfinished">Электронная почта</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="180"/>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="215"/>
        <source>Please enter a valid username.</source>
        <translation type="unfinished">Пожалуйста, введите действительное имя пользователя.</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="187"/>
        <source>Please enter a valid email address.
 </source>
        <translation type="unfinished">Пожалуйста, введите действительный адрес электронной почты.</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="200"/>
        <source>Username is existing.</source>
        <translation type="unfinished">Имя пользователя уже существует.</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="208"/>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="230"/>
        <source>Email is existing.</source>
        <translation type="unfinished">Электронная почта уже существует.</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="218"/>
        <source>Please enter the full name.</source>
        <translation type="unfinished">Пожалуйста, введите полное имя.</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="221"/>
        <source>Please choose an user group.</source>
        <translation type="unfinished">Пожалуйста, выберите группу пользователей.</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="224"/>
        <source>Please choose a subsystem.</source>
        <translation type="unfinished">Пожалуйста, выберите подсистему.</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="227"/>
        <source>Please enter a valid password.</source>
        <translation type="unfinished">Пожалуйста, введите действительный пароль.</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="237"/>
        <source>Please select an avatar image.</source>
        <translation type="unfinished">Пожалуйста, выберите изображение аватара.</translation>
    </message>
</context>
<context>
    <name>CalendarComboBox</name>
    <message>
        <location filename="../common/widget/event/calendar_combobox.py" line="20"/>
        <source>Time</source>
        <translation type="unfinished">Время</translation>
    </message>
</context>
<context>
    <name>CalendarPickerWidget</name>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="52"/>
        <source>January</source>
        <translation type="unfinished">Январь</translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="52"/>
        <source>February</source>
        <translation type="unfinished">Февраль</translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="52"/>
        <source>March</source>
        <translation type="unfinished">Март</translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="52"/>
        <source>April</source>
        <translation type="unfinished">Апрель</translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="53"/>
        <source>May</source>
        <translation type="unfinished">Май</translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="53"/>
        <source>June</source>
        <translation type="unfinished">Июнь</translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="53"/>
        <source>July</source>
        <translation type="unfinished">Июль</translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="53"/>
        <source>August</source>
        <translation type="unfinished">Август</translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="53"/>
        <source>September</source>
        <translation type="unfinished">Сентябрь</translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="54"/>
        <source>October</source>
        <translation type="unfinished">Октябрь</translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="54"/>
        <source>November</source>
        <translation type="unfinished">Ноябрь</translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="55"/>
        <source>December</source>
        <translation type="unfinished">Декабрь</translation>
    </message>
</context>
<context>
    <name>CameraBottomToolbarWidget</name>
    <message>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="348"/>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="356"/>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="364"/>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="372"/>
        <source>Divisions</source>
        <translation type="unfinished">Подразделения</translation>
    </message>
    <message>
        <source>Exit all</source>
        <translation type="obsolete">Выйти из всего</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="74"/>
        <source>Save</source>
        <translation type="unfinished">Сохранить</translation>
    </message>
    <message>
        <source>Grid</source>
        <translation type="obsolete">Сетка</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="221"/>
        <source>Main</source>
        <translation type="unfinished">Основной</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="223"/>
        <source>Sub</source>
        <translation type="unfinished">Под</translation>
    </message>
    <message>
        <source>Video Stream</source>
        <translation type="obsolete">Видеопоток</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="83"/>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="439"/>
        <source>Record Video</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="92"/>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="441"/>
        <source>Capture</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="99"/>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="443"/>
        <source>Microphone</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="106"/>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="445"/>
        <source>Volume</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="117"/>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="447"/>
        <source>Full screen</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="176"/>
        <source>Edit</source>
        <translation type="unfinished">Редактировать</translation>
    </message>
</context>
<context>
    <name>CameraGridWidget</name>
    <message>
        <source>Server Offline</source>
        <translation type="obsolete">Сервер отключен</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_grid_widget.py" line="116"/>
        <source>Please enlarge the grid cell or switch to a grid size smaller than 3x3.</source>
        <translation type="unfinished">Пожалуйста, увеличьте размер ячейки сетки или установите размер сетки меньше 3x3.</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_grid_widget.py" line="119"/>
        <source>To drag an item onto the Map, you need to enter Map edit mode.</source>
        <translation type="unfinished">Чтобы перетащить элемент на карту, необходимо войти в режим редактирования карты.</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_grid_widget.py" line="122"/>
        <source>Map saved successfully.</source>
        <translation type="unfinished">Карта успешно сохранена.</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_grid_widget.py" line="125"/>
        <source>Failed to save map.</source>
        <translation type="unfinished">Не удалось сохранить карту.</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_grid_widget.py" line="128"/>
        <source>This camera is already assigned to another position on the map.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_grid_widget.py" line="133"/>
        <source>Floor saved successfully.</source>
        <translation type="unfinished">Этаж успешно сохранён</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_grid_widget.py" line="136"/>
        <source>Failed to save floor.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CameraInfoDialog</name>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="115"/>
        <source>Camera Configuration</source>
        <translation type="unfinished">Конфигурация камеры</translation>
    </message>
    <message>
        <source>CAMERA INFORMATION</source>
        <translation type="obsolete">ИНФОРМАЦИЯ О КАМЕРЕ</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="40"/>
        <source>Camera Information</source>
        <translation type="unfinished">ИНФОРМАЦИЯ О КАМЕРЕ</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="117"/>
        <source>Recording Configuration</source>
        <translation type="unfinished">Настройка записи</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="133"/>
        <source>Camera Name</source>
        <translation type="unfinished">Имя камеры</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="137"/>
        <source>Camera URL</source>
        <translation type="unfinished">URL камеры</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="147"/>
        <source>Group Camera</source>
        <translation type="unfinished">Групповая камера</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="151"/>
        <source>Description</source>
        <translation type="unfinished">Описание</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="155"/>
        <source>Latitude</source>
        <translation type="unfinished">Широта</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="159"/>
        <source>Longitude</source>
        <translation type="unfinished">Долгота</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="168"/>
        <source>Status</source>
        <translation type="unfinished">Статус</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="176"/>
        <source>Find address</source>
        <translation type="unfinished">Найти адрес</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="177"/>
        <source>Find coordinates</source>
        <translation type="unfinished">Найти координаты</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="216"/>
        <source>Address</source>
        <translation type="unfinished">Адрес</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="216"/>
        <source>Enter address</source>
        <translation type="unfinished">Введите адрес</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="178"/>
        <source>Open map</source>
        <translation type="unfinished">Открыть карту</translation>
    </message>
    <message>
        <source>Recording</source>
        <translation type="obsolete">Запись</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="398"/>
        <source>Latitude or Longitude is empty</source>
        <translation type="unfinished">Широта или долгота не заполнены.</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="402"/>
        <source>Finding address...</source>
        <translation type="unfinished">Поиск адреса...</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="405"/>
        <source>Latitude and Longitude must be a number</source>
        <translation type="unfinished">Широта и долгота должны быть числами.</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="413"/>
        <source>Finding coords...</source>
        <translation type="unfinished">Поиск координат...</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="417"/>
        <source>Address is empty</source>
        <translation type="unfinished">Адрес не заполнен.</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="421"/>
        <source>Finding address failed</source>
        <translation type="unfinished">Не удалось найти адрес.</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="428"/>
        <source>Finding coordinate failed</source>
        <translation type="unfinished">Не удалось найти координаты.</translation>
    </message>
    <message>
        <source>Resolution(*)</source>
        <translation type="obsolete">Разрешение(*)</translation>
    </message>
    <message>
        <source>Main Stream</source>
        <translation type="obsolete">Основной поток</translation>
    </message>
    <message>
        <source>Sub Stream</source>
        <translation type="obsolete">Вторичный поток</translation>
    </message>
    <message>
        <source>Brightness</source>
        <translation type="obsolete">Яркость</translation>
    </message>
    <message>
        <source>Sharpness</source>
        <translation type="obsolete">Резкость</translation>
    </message>
    <message>
        <source>Contrast</source>
        <translation type="obsolete">Контраст</translation>
    </message>
    <message>
        <source>Saturation</source>
        <translation type="obsolete">Насыщенность</translation>
    </message>
    <message>
        <source>Video Adjustment</source>
        <translation type="obsolete">Настройка видео</translation>
    </message>
    <message>
        <source>Record Quality (*)</source>
        <translation type="obsolete">Качество записи (*)</translation>
    </message>
    <message>
        <source>Resolution</source>
        <translation type="obsolete">Разрешение</translation>
    </message>
    <message>
        <source>Timelapse speed</source>
        <translation type="obsolete">Скорость таймлапса</translation>
    </message>
    <message>
        <source>Record Segment Interval (*)</source>
        <translation type="obsolete">Интервал записи сегмента (*)</translation>
    </message>
    <message>
        <source>Record Setting</source>
        <translation type="obsolete">Настройки записи</translation>
    </message>
</context>
<context>
    <name>CameraScreen</name>
    <message>
        <location filename="../presentation/camera_screen/camera_screen.py" line="804"/>
        <source>This ShortcutID does not exist.</source>
        <translation type="unfinished">Этот ShortcutID не существует.</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1022"/>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1024"/>
        <source>Virtual Window </source>
        <translation type="unfinished">Виртуальное окно </translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1036"/>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1038"/>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1064"/>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1066"/>
        <source>View </source>
        <translation type="unfinished">Вид </translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1050"/>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1052"/>
        <source>Map </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1508"/>
        <source>Editing </source>
        <translation type="unfinished">Редактирование </translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1536"/>
        <source>Editing digital map</source>
        <translation type="unfinished">Редактирование цифровой карты</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1559"/>
        <source>No camera selected</source>
        <translation type="unfinished">Нет выбранной камеры</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1691"/>
        <source>Open the left sidebar</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1694"/>
        <source>Close the left sidebar</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1705"/>
        <source>Open the event bar</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1708"/>
        <source>Close the event bar</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CameraSettingDialog</name>
    <message>
        <source>All</source>
        <translation type="obsolete">Все</translation>
    </message>
    <message>
        <source>Camera Name</source>
        <translation type="obsolete">Имя камеры</translation>
    </message>
    <message>
        <source>Status</source>
        <translation type="obsolete">Статус</translation>
    </message>
    <message>
        <source>Action</source>
        <translation type="obsolete">Действие</translation>
    </message>
    <message>
        <source>Cancel</source>
        <translation type="obsolete">Отмена</translation>
    </message>
</context>
<context>
    <name>CameraWidget</name>
    <message>
        <source>Connecting...</source>
        <translation type="obsolete">Подключение...</translation>
    </message>
    <message>
        <source>Disconnect</source>
        <translation type="obsolete">Отключить</translation>
    </message>
    <message>
        <source>Clear Selection</source>
        <translation type="obsolete">Очистить выбор</translation>
    </message>
    <message>
        <source>Zoom to Selection</source>
        <translation type="obsolete">Приблизить к выбору</translation>
    </message>
    <message>
        <source>Export video</source>
        <translation type="obsolete">Экспортировать видео</translation>
    </message>
    <message>
        <source>Disconnected</source>
        <translation type="obsolete">Отключено</translation>
    </message>
    <message>
        <source>Unauthorized Access</source>
        <translation type="obsolete">Несанкционированный доступ</translation>
    </message>
</context>
<context>
    <name>ChangeModeButtonSideMenu</name>
    <message>
        <location filename="../common/widget/custom_change_mode.py" line="27"/>
        <source>All</source>
        <translation type="unfinished">Все</translation>
    </message>
    <message>
        <location filename="../common/widget/custom_change_mode.py" line="31"/>
        <source>Server</source>
        <translation type="unfinished">Сервер</translation>
    </message>
    <message>
        <location filename="../common/widget/custom_change_mode.py" line="35"/>
        <source>Camera Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_change_mode.py" line="39"/>
        <source>Virtual Window</source>
        <translation type="unfinished">Виртуальное окно</translation>
    </message>
    <message>
        <location filename="../common/widget/custom_change_mode.py" line="43"/>
        <source>Saved View</source>
        <translation type="unfinished">Сохранённый вид</translation>
    </message>
    <message>
        <location filename="../common/widget/custom_change_mode.py" line="47"/>
        <source>Map</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ChildEventWidget</name>
    <message>
        <location filename="../common/widget/event_bar.py" line="1092"/>
        <source>Loading...</source>
        <translation type="unfinished">Загрузка...</translation>
    </message>
</context>
<context>
    <name>ComboBoxWithRequireField</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="2261"/>
        <source>Choose one</source>
        <translation type="unfinished">Выберите один</translation>
    </message>
</context>
<context>
    <name>ConfigCameraFovDialog</name>
    <message>
        <source>Size (text &amp; icon)</source>
        <translation type="obsolete">Размер (текст и иконка)</translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="101"/>
        <source>Camera Settings</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="108"/>
        <source>Size</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="144"/>
        <source>Small</source>
        <translation type="unfinished">Маленький</translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="164"/>
        <source>Medium</source>
        <translation type="unfinished">Среднее</translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="183"/>
        <source>Large</source>
        <translation type="unfinished">Большой</translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="210"/>
        <source>Show as:</source>
        <translation type="unfinished">Отображать как:</translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="223"/>
        <source>Icon</source>
        <translation type="unfinished">Иконка</translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="256"/>
        <source>Shape</source>
        <translation type="unfinished">Форма</translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="403"/>
        <source>Color</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="579"/>
        <source>Delete</source>
        <translation type="unfinished">Удалить</translation>
    </message>
    <message>
        <source>Shape &amp; Cone Color</source>
        <translation type="obsolete">Форма и цвет конуса</translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="459"/>
        <source>Preferences</source>
        <translation type="unfinished">Настройки</translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="505"/>
        <source>Show field of view</source>
        <translation type="unfinished">Показать поле обзора</translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="553"/>
        <source>Show name</source>
        <translation type="unfinished">Показать имя</translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="562"/>
        <source>Redraw</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ConfigTracingScriptDialog</name>
    <message>
        <source>Script Name (*)</source>
        <translation type="obsolete">Имя скрипта (*)</translation>
    </message>
    <message>
        <source>The camera list is empty, please add a camera to the script first.</source>
        <translation type="obsolete">Список камер пуст, пожалуйста, сначала добавьте камеру в скрипт.</translation>
    </message>
    <message>
        <source>Select screen</source>
        <translation type="obsolete">Выберите экран</translation>
    </message>
    <message>
        <source>Choose a screen to display this Script:</source>
        <translation type="obsolete">Выберите экран для отображения этого скрипта:</translation>
    </message>
    <message>
        <source>Please choose a screen to display this script.</source>
        <translation type="obsolete">Пожалуйста, выберите экран для отображения этого скрипта.</translation>
    </message>
    <message>
        <source>Updated Tracking Scripts successfully.</source>
        <translation type="obsolete">Скрипты отслеживания успешно обновлены.</translation>
    </message>
    <message>
        <source>Added Tracking Scripts successfully.</source>
        <translation type="obsolete">Скрипты отслеживания успешно добавлены.</translation>
    </message>
    <message>
        <source>Please Enter Complete Information.</source>
        <translation type="obsolete">Пожалуйста, введите полную информацию.</translation>
    </message>
</context>
<context>
    <name>ConfirmDialog</name>
    <message>
        <source>Cancel</source>
        <translation type="obsolete">Отмена</translation>
    </message>
    <message>
        <source>Confirm</source>
        <translation type="obsolete">Подтвердить</translation>
    </message>
</context>
<context>
    <name>ContentAddUpdateScriptAIDialog</name>
    <message>
        <source>ADD NEW AI SCRIPT</source>
        <translation type="obsolete">ДОБАВИТЬ НОВЫЙ СКРИПТ ИИ</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="82"/>
        <source>EDIT AI SCRIPT</source>
        <translation type="unfinished">РЕДАКТИРОВАТЬ СКРИПТ ИИ</translation>
    </message>
    <message>
        <source>Human Indentify</source>
        <translation type="obsolete">Идентификация человека</translation>
    </message>
    <message>
        <source>Vehicle Indentify</source>
        <translation type="obsolete">Идентификация транспортного средства</translation>
    </message>
    <message>
        <source>Human Intrusion</source>
        <translation type="obsolete">Вторжение человека</translation>
    </message>
    <message>
        <source>Vehicle Intrusion</source>
        <translation type="obsolete">Вторжение транспортного средства</translation>
    </message>
    <message>
        <source>Human Flow</source>
        <translation type="obsolete">Поток людей</translation>
    </message>
    <message>
        <source>Vehicle Flow</source>
        <translation type="obsolete">Поток транспортных средств</translation>
    </message>
    <message>
        <source>Human Access Control</source>
        <translation type="obsolete">Контроль доступа человека</translation>
    </message>
    <message>
        <source>Vehicle Access Control</source>
        <translation type="obsolete">Контроль доступа транспортного средства</translation>
    </message>
    <message>
        <source>Human Weapon</source>
        <translation type="obsolete">Оружие человека</translation>
    </message>
    <message>
        <source>Vehicle Weapon</source>
        <translation type="obsolete">Оружие транспортного средства</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="83"/>
        <source>Recognition</source>
        <translation type="unfinished">Распознавание</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="84"/>
        <source>Protection</source>
        <translation type="unfinished">Защита</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="85"/>
        <source>Frequency</source>
        <translation type="unfinished">Частота</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="86"/>
        <source>Access</source>
        <translation type="unfinished">Доступ</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="87"/>
        <source>Motion</source>
        <translation type="unfinished">Движение</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="88"/>
        <source>Traffic</source>
        <translation type="unfinished">Поток</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="89"/>
        <source>Weapon</source>
        <translation type="unfinished">Оружие</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="90"/>
        <source>UFO</source>
        <translation type="unfinished">Внеземные объекты</translation>
    </message>
    <message>
        <source>AI Type: </source>
        <translation type="obsolete">Тип ИИ: </translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="175"/>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="304"/>
        <source>*</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="370"/>
        <source>AI Type</source>
        <translation type="unfinished">Тип ИИ</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="145"/>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="384"/>
        <source>Update frequency:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="385"/>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="408"/>
        <source>Seconds</source>
        <translation type="unfinished">Секунды</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="143"/>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="388"/>
        <source>Confidence threshold:</source>
        <translation type="unfinished">Порог достоверности:</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="148"/>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="393"/>
        <source>Tracking time:</source>
        <translation type="unfinished">Время отслеживания:</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="393"/>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="403"/>
        <source>Minutes</source>
        <translation type="unfinished">Минуты</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="150"/>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="398"/>
        <source>Appearance count:</source>
        <translation type="unfinished">Количество появлений:</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="398"/>
        <source>Times</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="153"/>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="403"/>
        <source>Disappear time:</source>
        <translation type="unfinished">Время исчезновения:</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="91"/>
        <source>Fire</source>
        <translation type="unfinished">Пожар</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="155"/>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="408"/>
        <source>Standing time:</source>
        <translation type="unfinished">Время стояния:</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="442"/>
        <source>Draw active zone</source>
        <translation type="unfinished">Нарисовать активную зону</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="603"/>
        <source>Error</source>
        <translation type="unfinished">Ошибка</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="615"/>
        <source>Update frequency must be greater than zero or equal to zero.</source>
        <translation type="unfinished">Частота обновления должна быть больше нуля или равна нулю.</translation>
    </message>
    <message>
        <source>Update frequency must be greater than zero.</source>
        <translation type="obsolete">Частота обновления должна быть больше нуля.</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="621"/>
        <source>Confidence threshold must be between 0 and 100.</source>
        <translation type="unfinished">Порог достоверности должен быть между 0 и 100.</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="628"/>
        <source>Tracking time must be greater than zero.</source>
        <translation type="unfinished">Время отслеживания должно быть больше нуля.</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="633"/>
        <source>Appearance count must be greater than zero.</source>
        <translation type="unfinished">Количество появлений должно быть больше нуля.</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="639"/>
        <source>Disappear time must be greater than zero.</source>
        <translation type="unfinished">Время исчезновения должно быть больше нуля.</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="645"/>
        <source>Standing time must be greater than zero.</source>
        <translation type="unfinished">Время стояния должно быть больше нуля.</translation>
    </message>
    <message>
        <source>Alarm delay must be non-negative.</source>
        <translation type="obsolete">Задержка сигнала должна быть неотрицательной.</translation>
    </message>
    <message>
        <source>Minimum face size must be greater than zero.</source>
        <translation type="obsolete">Минимальный размер лица должен быть больше нуля.</translation>
    </message>
    <message>
        <source>Invalid input. Please enter numeric values only.</source>
        <translation type="obsolete">Неверный ввод. Пожалуйста, введите только числовые значения.</translation>
    </message>
</context>
<context>
    <name>ControllerAiWidget</name>
    <message>
        <source>Face</source>
        <translation type="obsolete">Лицо</translation>
    </message>
    <message>
        <source>Vehicle</source>
        <translation type="obsolete">Транспортное средство</translation>
    </message>
    <message>
        <source>Crowd</source>
        <translation type="obsolete">Толпа</translation>
    </message>
</context>
<context>
    <name>CreateBuildingDialog</name>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3395"/>
        <source>Building name</source>
        <translation type="unfinished">Назвать здание</translation>
    </message>
    <message>
        <source>Create</source>
        <translation type="obsolete">Создать</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3405"/>
        <source>Create Building</source>
        <translation type="unfinished">Создать здание</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3408"/>
        <source>Edit Building</source>
        <translation type="unfinished">Редактировать здание</translation>
    </message>
</context>
<context>
    <name>CreateFloorDialog</name>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3359"/>
        <source>Name floor</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3372"/>
        <source>Create floor</source>
        <translation type="unfinished">Создать этаж</translation>
    </message>
</context>
<context>
    <name>CustomComboBox</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="650"/>
        <source>Object Quantity</source>
        <translation type="unfinished">Количество объектов</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="680"/>
        <source>Please select</source>
        <translation type="unfinished">Пожалуйста, выберите</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="683"/>
        <source>High (Default)</source>
        <translation type="unfinished">Высокое (по умолчанию)</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="686"/>
        <source>Medium</source>
        <translation type="unfinished">Среднее</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="689"/>
        <source>Low</source>
        <translation type="unfinished">Низкое</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="692"/>
        <source>x1 (Default)</source>
        <translation type="unfinished">x1 (по умолчанию)</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="695"/>
        <source>15 minutes (Default)</source>
        <translation type="unfinished">15 минут (по умолчанию)</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="698"/>
        <source>20 minutes</source>
        <translation type="unfinished">20 минут</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="701"/>
        <source>25 minutes</source>
        <translation type="unfinished">25 минут</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="704"/>
        <source>56 (Default)</source>
        <translation type="unfinished">56 (по умолчанию)</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="707"/>
        <source>0.5 (Default)</source>
        <translation type="unfinished">0.5 (по умолчанию)</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="710"/>
        <source>0.48 (Default)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="713"/>
        <source>21 (Default)</source>
        <translation type="unfinished">21 (по умолчанию)</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="716"/>
        <source>5 (Default)</source>
        <translation type="unfinished">5 (по умолчанию)</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="719"/>
        <source>10 Object Quantity</source>
        <translation type="unfinished">10 Количество объектов</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="722"/>
        <source>20 Object Quantity</source>
        <translation type="unfinished">20 Количество объектов</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="725"/>
        <source>30 Object Quantity</source>
        <translation type="unfinished">30 Количество объектов</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="728"/>
        <source>40 Object Quantity</source>
        <translation type="unfinished">40 Количество объектов</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="731"/>
        <source>50 Object Quantity</source>
        <translation type="unfinished">50 Количество объектов</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="734"/>
        <source>60 Object Quantity</source>
        <translation type="unfinished">60 Количество объектов</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="737"/>
        <source>70 Object Quantity</source>
        <translation type="unfinished">70 Количество объектов</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="740"/>
        <source>80 Object Quantity</source>
        <translation type="unfinished">80 Количество объектов</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="743"/>
        <source>90 Object Quantity</source>
        <translation type="unfinished">90 Количество объектов</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="746"/>
        <source>100 Object Quantity</source>
        <translation type="unfinished">100 Количество объектов</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="749"/>
        <source>200 Object Quantity</source>
        <translation type="unfinished">200 Количество объектов</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="752"/>
        <source>300 Object Quantity</source>
        <translation type="unfinished">300 Количество объектов</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="755"/>
        <source>400 Object Quantity</source>
        <translation type="unfinished">400 Количество объектов</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="758"/>
        <source>500 Object Quantity</source>
        <translation type="unfinished">500 Количество объектов</translation>
    </message>
</context>
<context>
    <name>CustomMenuForCameraRightClick</name>
    <message>
        <source>Turn off AI</source>
        <translation type="obsolete">Выключить ИИ</translation>
    </message>
    <message>
        <source>Human intrustion</source>
        <translation type="obsolete">Вторжение</translation>
    </message>
    <message>
        <source>Human flow</source>
        <translation type="obsolete">Поток</translation>
    </message>
    <message>
        <source>Human access control</source>
        <translation type="obsolete">Контроль</translation>
    </message>
    <message>
        <source>Human weapon</source>
        <translation type="obsolete">Оружие</translation>
    </message>
    <message>
        <source>Human identification</source>
        <translation type="obsolete">Идентификация</translation>
    </message>
    <message>
        <source>Vehicle intrusion</source>
        <translation type="obsolete">Вторжение</translation>
    </message>
    <message>
        <source>Vehicle flow</source>
        <translation type="obsolete">Поток</translation>
    </message>
    <message>
        <source>Vehicle access control</source>
        <translation type="obsolete">Контроль</translation>
    </message>
    <message>
        <source>Vehicle identification</source>
        <translation type="obsolete">Идентификация</translation>
    </message>
    <message>
        <source>Open camera to ...    </source>
        <translation type="obsolete">Открыть камеру для ...    </translation>
    </message>
    <message>
        <source>Remove form view	Del</source>
        <translation type="obsolete">Удалить из просмотра		Del</translation>
    </message>
    <message>
        <source>Rename	F2</source>
        <translation type="obsolete">Переименовать	F2</translation>
    </message>
    <message>
        <source>Recognition</source>
        <translation type="obsolete">Распознавание</translation>
    </message>
    <message>
        <source>Protection</source>
        <translation type="obsolete">Защита</translation>
    </message>
    <message>
        <source>Frequency</source>
        <translation type="obsolete">Частота</translation>
    </message>
    <message>
        <source>Access</source>
        <translation type="obsolete">Доступ</translation>
    </message>
    <message>
        <source>Motion</source>
        <translation type="obsolete">Движение</translation>
    </message>
    <message>
        <source>Traffic</source>
        <translation type="obsolete">Поток</translation>
    </message>
    <message>
        <source>Weapon</source>
        <translation type="obsolete">Оружие</translation>
    </message>
    <message>
        <source>UFO</source>
        <translation type="obsolete">Внеземные объекты</translation>
    </message>
    <message>
        <source>AI flow</source>
        <translation type="obsolete">Поток ИИ</translation>
    </message>
    <message>
        <source>Video streams</source>
        <translation type="obsolete">Видеопотоки</translation>
    </message>
    <message>
        <source>Setting	I</source>
        <translation type="obsolete">Настройки I</translation>
    </message>
    <message>
        <source>Exit full-screen	Esc</source>
        <translation type="obsolete">Выйти из полноэкранного режима	Esc</translation>
    </message>
    <message>
        <source>Full-screen	Enter</source>
        <translation type="obsolete">Полноэкранный режим	Enter</translation>
    </message>
</context>
<context>
    <name>CustomMenuForEventRightClick</name>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="345"/>
        <source>New View</source>
        <translation type="unfinished">Новый вид</translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="352"/>
        <source>New Saved View</source>
        <translation type="unfinished">Новый сохранённый вид</translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="358"/>
        <source>New Virtual Window</source>
        <translation type="unfinished">Новое виртуальное окно</translation>
    </message>
</context>
<context>
    <name>CustomMenuWithCheckbox</name>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="106"/>
        <source>Main</source>
        <translation type="unfinished">Основной</translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="111"/>
        <source>Sub</source>
        <translation type="unfinished">Под</translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="116"/>
        <source>AI</source>
        <translation type="unfinished">AI</translation>
    </message>
</context>
<context>
    <name>CustomVideoOutput</name>
    <message>
        <location filename="../common/qml/map/CustomVideoOutput.qml" line="10"/>
        <location filename="../common/qml/map/CustomVideoOutput.qml" line="56"/>
        <source>No Data</source>
        <translation type="unfinished">Нет данных</translation>
    </message>
    <message>
        <location filename="../common/qml/map/CustomVideoOutput.qml" line="38"/>
        <location filename="../common/qml/map/CustomVideoOutput.qml" line="46"/>
        <source>Connecting</source>
        <translation type="unfinished">Подключение...</translation>
    </message>
    <message>
        <location filename="../common/qml/map/CustomVideoOutput.qml" line="56"/>
        <source>Disconnected</source>
        <translation type="unfinished">Отключено</translation>
    </message>
</context>
<context>
    <name>DeviceController</name>
    <message>
        <location filename="../common/qml/models/device_controller.py" line="586"/>
        <source>The camera is recording. Are you sure you want to delete?</source>
        <translation type="unfinished">Камера записывает. Вы уверены, что хотите удалить?</translation>
    </message>
    <message>
        <location filename="../common/qml/models/device_controller.py" line="625"/>
        <source>Edit group</source>
        <translation type="unfinished">Редактировать группу</translation>
    </message>
    <message>
        <source>EDIT GROUP</source>
        <translation type="obsolete">Редактировать группу</translation>
    </message>
</context>
<context>
    <name>DeviceGroupTable</name>
    <message>
        <source>GROUP NAME</source>
        <translation type="obsolete">Название группы</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="60"/>
        <source>Box</source>
        <translation type="unfinished">AI-ящик</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="69"/>
        <source>Camera</source>
        <translation type="unfinished">Камера</translation>
    </message>
    <message>
        <source>DOOR</source>
        <translation type="obsolete">Дверь</translation>
    </message>
    <message>
        <source>ACTION</source>
        <translation type="obsolete">ДЕЙСТВИЕ</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="164"/>
        <source>Box (0)</source>
        <translation type="unfinished">БОКС (0)</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="173"/>
        <source>Camera (0)</source>
        <translation type="unfinished">Камера (0)</translation>
    </message>
    <message>
        <source>REVOLVING DOOR (0)</source>
        <translation type="obsolete">ВРАЩАЮЩАЯСЯ ДВЕРЬ (0)</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="259"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="625"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="952"/>
        <source>Recognition &amp; Protection (%1)</source>
        <translation type="unfinished">Признание и защита (%1)</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="372"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="743"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="1054"/>
        <source>Risk Recognition (%1)</source>
        <translation type="unfinished">Распознавание риска (%1)</translation>
    </message>
    <message>
        <source>Recognition &amp; Protection</source>
        <translation type="obsolete">Распознавание и защита</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="51"/>
        <source>Group name</source>
        <translation type="unfinished">Название группы</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="87"/>
        <source>Action</source>
        <translation type="unfinished">Действие</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="274"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="640"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="962"/>
        <source>Recognition</source>
        <translation type="unfinished">Распознавание</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="285"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="652"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="974"/>
        <source>Protection</source>
        <translation type="unfinished">Защита</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="296"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="664"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="986"/>
        <source>Frequency</source>
        <translation type="unfinished">Частота</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="307"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="676"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="998"/>
        <source>Access</source>
        <translation type="unfinished">Доступ</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="318"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="688"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="1010"/>
        <source>Motion</source>
        <translation type="unfinished">Движение</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="329"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="700"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="1022"/>
        <source>Traffic</source>
        <translation type="unfinished">Поток</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="410"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="782"/>
        <source>Fire</source>
        <translation type="unfinished">Пожар</translation>
    </message>
    <message>
        <source>Risk Recognition</source>
        <translation type="obsolete">Распознавание рисков</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="399"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="770"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="1074"/>
        <source>UFO</source>
        <translation type="unfinished">Внеземные объекты</translation>
    </message>
    <message>
        <source>Human </source>
        <translation type="obsolete">Человек </translation>
    </message>
    <message>
        <source>Intrusion</source>
        <translation type="obsolete">Вторжение</translation>
    </message>
    <message>
        <source>Flow</source>
        <translation type="obsolete">Поток</translation>
    </message>
    <message>
        <source>Access Control</source>
        <translation type="obsolete">Контроль</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="388"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="758"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="1062"/>
        <source>Weapon</source>
        <translation type="unfinished">Оружие</translation>
    </message>
    <message>
        <source>Identification</source>
        <translation type="obsolete">Идентификация</translation>
    </message>
    <message>
        <source>Vehicle </source>
        <translation type="obsolete">Транспортное средство </translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="483"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="842"/>
        <source>Number of AI selected</source>
        <translation type="unfinished">Количество выбранных ИИ</translation>
    </message>
</context>
<context>
    <name>DeviceTable</name>
    <message>
        <source>DEVICE NAME</source>
        <translation type="obsolete">ИМЯ УСТРОЙСТВА</translation>
    </message>
    <message>
        <source>BRANCH</source>
        <translation type="obsolete">ФИЛИАЛ</translation>
    </message>
    <message>
        <source>MODEL</source>
        <translation type="obsolete">Модель</translation>
    </message>
    <message>
        <source>IP ADDRESS</source>
        <translation type="obsolete">IP-АДРЕС</translation>
    </message>
    <message>
        <source>MAC ADDRESS</source>
        <translation type="obsolete">MAC-АДРЕС</translation>
    </message>
    <message>
        <source>PARTNER</source>
        <translation type="obsolete">ПАРТНЁР</translation>
    </message>
    <message>
        <source>GROUP</source>
        <translation type="obsolete">ГРУППА</translation>
    </message>
    <message>
        <source>ACTION</source>
        <translation type="obsolete">ДЕЙСТВИЕ</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="54"/>
        <source>Device name</source>
        <translation type="unfinished">ИМЯ УСТРОЙСТВА</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="63"/>
        <source>Branch</source>
        <translation type="unfinished">Филиал</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="72"/>
        <source>Model</source>
        <translation type="unfinished">Модель</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="81"/>
        <source>IP address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="90"/>
        <source>Mac address</source>
        <translation type="unfinished">MAC-адрес</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="99"/>
        <source>Partner</source>
        <translation type="unfinished">Партнёр</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="108"/>
        <source>Group</source>
        <translation type="unfinished">Группа</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="117"/>
        <source>Action</source>
        <translation type="unfinished">Действие</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="321"/>
        <source>Recognition &amp; Protection (%1)</source>
        <translation type="unfinished">Признание и защита (%1)</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="475"/>
        <source>Risk Recognition (%1)</source>
        <translation type="unfinished">Распознавание риска (%1)</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="545"/>
        <source>Fire</source>
        <translation type="unfinished">Пожар</translation>
    </message>
    <message>
        <source>Recognition &amp; Protection</source>
        <translation type="obsolete">Распознавание и защита</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="364"/>
        <source>Recognition</source>
        <translation type="unfinished">Распознавание</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="377"/>
        <source>Protection</source>
        <translation type="unfinished">Защита</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="390"/>
        <source>Frequency</source>
        <translation type="unfinished">Частота</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="403"/>
        <source>Access</source>
        <translation type="unfinished">Доступ</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="416"/>
        <source>Motion</source>
        <translation type="unfinished">Движение</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="429"/>
        <source>Traffic</source>
        <translation type="unfinished">Поток</translation>
    </message>
    <message>
        <source>Risk Recognition</source>
        <translation type="obsolete">Распознавание рисков</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="532"/>
        <source>UFO</source>
        <translation type="unfinished">Внеземные объекты</translation>
    </message>
    <message>
        <source>Human </source>
        <translation type="obsolete">Человек </translation>
    </message>
    <message>
        <source>Intrusion</source>
        <translation type="obsolete">Вторжение</translation>
    </message>
    <message>
        <source>Flow</source>
        <translation type="obsolete">Поток</translation>
    </message>
    <message>
        <source>Access Control</source>
        <translation type="obsolete">Контроль</translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="519"/>
        <source>Weapon</source>
        <translation type="unfinished">Оружие</translation>
    </message>
    <message>
        <source>Identification</source>
        <translation type="obsolete">Идентификация</translation>
    </message>
    <message>
        <source>Vehicle </source>
        <translation type="obsolete">Транспортное средство </translation>
    </message>
</context>
<context>
    <name>DialogEditGridLayouts</name>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="78"/>
        <source>EDIT GRID LAYOUTS</source>
        <translation type="unfinished">Редактировать макеты сетки</translation>
    </message>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="110"/>
        <source>Cancel</source>
        <translation type="unfinished">Отмена</translation>
    </message>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="121"/>
        <source>Save</source>
        <translation type="unfinished">Сохранить</translation>
    </message>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="142"/>
        <source>Columns: </source>
        <translation type="unfinished">Колонки: </translation>
    </message>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="143"/>
        <source>Rows: </source>
        <translation type="unfinished">Строки: </translation>
    </message>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="176"/>
        <source>Layouts:</source>
        <translation type="unfinished">Макеты:</translation>
    </message>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="212"/>
        <source>Restore to Default</source>
        <translation type="unfinished">Восстановить по умолчанию</translation>
    </message>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="266"/>
        <source> Divisions</source>
        <translation type="unfinished"> Подразделения</translation>
    </message>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="288"/>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="296"/>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="303"/>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="311"/>
        <source>Divisions</source>
        <translation type="unfinished">Подразделения</translation>
    </message>
</context>
<context>
    <name>EventBar</name>
    <message>
        <location filename="../common/widget/event_bar.py" line="119"/>
        <location filename="../common/widget/event_bar.py" line="613"/>
        <source>Realtime Events</source>
        <translation type="unfinished">События в реальном времени</translation>
    </message>
    <message>
        <location filename="../common/widget/event_bar.py" line="125"/>
        <location filename="../common/widget/event_bar.py" line="616"/>
        <source>Warning</source>
        <translation type="unfinished">Предупреждение</translation>
    </message>
    <message>
        <location filename="../common/widget/event_bar.py" line="270"/>
        <location filename="../common/widget/event_bar.py" line="619"/>
        <source>No search results</source>
        <translation type="unfinished">Нет результатов поиска</translation>
    </message>
    <message>
        <source>Group</source>
        <translation type="obsolete">Группа</translation>
    </message>
    <message>
        <location filename="../common/widget/event_bar.py" line="150"/>
        <source>Filter</source>
        <translation type="unfinished">Фильтр</translation>
    </message>
    <message>
        <location filename="../common/widget/event_bar.py" line="158"/>
        <source>refresh</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>No results found</source>
        <translation type="obsolete">Результаты не найдены</translation>
    </message>
    <message>
        <location filename="../common/widget/event_bar.py" line="405"/>
        <location filename="../common/widget/event_bar.py" line="413"/>
        <location filename="../common/widget/event_bar.py" line="422"/>
        <location filename="../common/widget/event_bar.py" line="431"/>
        <source>All</source>
        <translation type="unfinished">Все</translation>
    </message>
</context>
<context>
    <name>EventDialog</name>
    <message>
        <source>Face</source>
        <translation type="obsolete">Лицо</translation>
    </message>
    <message>
        <source>License plate</source>
        <translation type="obsolete">Номерной знак</translation>
    </message>
    <message>
        <source>Access control</source>
        <translation type="obsolete">Контроль доступа</translation>
    </message>
    <message>
        <source>Crowd</source>
        <translation type="obsolete">Толпа</translation>
    </message>
    <message>
        <source>AI Flows: </source>
        <translation type="obsolete">Потоки ИИ: </translation>
    </message>
    <message>
        <source>Name: </source>
        <translation type="obsolete">Имя: </translation>
    </message>
    <message>
        <source>Status: </source>
        <translation type="obsolete">Статус: </translation>
    </message>
    <message>
        <source>Appear</source>
        <translation type="obsolete">Появиться</translation>
    </message>
    <message>
        <source>Time: </source>
        <translation type="obsolete">Время: </translation>
    </message>
    <message>
        <source>Cancel</source>
        <translation type="obsolete">Отмена</translation>
    </message>
    <message>
        <source>No group</source>
        <translation type="obsolete">Нет группы</translation>
    </message>
    <message>
        <source>Car</source>
        <translation type="obsolete">Автомобиль</translation>
    </message>
    <message>
        <source>Motor</source>
        <translation type="obsolete">Мотоцикл</translation>
    </message>
    <message>
        <source>Bicycle</source>
        <translation type="obsolete">Велосипед</translation>
    </message>
    <message>
        <source>Pedestrian</source>
        <translation type="obsolete">Пешеход</translation>
    </message>
</context>
<context>
    <name>EventWidget</name>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="262"/>
        <source>AI Flows: </source>
        <translation type="unfinished">Потоки ИИ: </translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="270"/>
        <source>Name: </source>
        <translation type="unfinished">Имя: </translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="283"/>
        <source>Status: </source>
        <translation type="unfinished">Статус: </translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="295"/>
        <source>Camera: </source>
        <translation type="unfinished">Камера: </translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="323"/>
        <source>Time: </source>
        <translation type="unfinished">Время: </translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="442"/>
        <source>No group</source>
        <translation type="unfinished">Нет группы</translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="483"/>
        <source>Car</source>
        <translation type="unfinished">Автомобиль</translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="485"/>
        <source>Motor</source>
        <translation type="unfinished">Мотоцикл</translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="487"/>
        <source>Bicycle</source>
        <translation type="unfinished">Велосипед</translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="489"/>
        <source>Pedestrian</source>
        <translation type="unfinished">Пешеход</translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="491"/>
        <source>Truck</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="493"/>
        <source>Bus</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="495"/>
        <source>Van</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="497"/>
        <source>Container truck</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="499"/>
        <source>Delivery tricycles</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="501"/>
        <source>Cyclo</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="503"/>
        <source>Ambulance</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="505"/>
        <source>Fire truck</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="507"/>
        <source>Wheelchair</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="509"/>
        <source>Trash car</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="511"/>
        <source>Tank truck</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="513"/>
        <source>Mixer truck</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="515"/>
        <source>Crane</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="517"/>
        <source>Roller</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="519"/>
        <source>Excavator</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="521"/>
        <source>Street Vendor</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="524"/>
        <location filename="../common/widget/event/event_widget.py" line="542"/>
        <source>Undefined</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="527"/>
        <source>Black</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="529"/>
        <source>White</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="531"/>
        <source>Red</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="533"/>
        <source>Blue</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="535"/>
        <source>Green</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="537"/>
        <source>Yellow</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="539"/>
        <source>Orange</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>FilterButtonSideMenu</name>
    <message>
        <location filename="../common/widget/custom_filter.py" line="47"/>
        <source>AI flows</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_filter.py" line="49"/>
        <source>State</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_filter.py" line="55"/>
        <source>Connected</source>
        <translation type="unfinished">Подключено</translation>
    </message>
    <message>
        <location filename="../common/widget/custom_filter.py" line="56"/>
        <source>Disconnected</source>
        <translation type="unfinished">Отключено</translation>
    </message>
    <message>
        <location filename="../common/widget/custom_filter.py" line="57"/>
        <source>Face Recognition</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_filter.py" line="59"/>
        <source>Vehicle</source>
        <translation type="unfinished">Транспортное средство</translation>
    </message>
</context>
<context>
    <name>FilterEventDialog</name>
    <message>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="97"/>
        <source>Status</source>
        <translation type="unfinished">Статус</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="104"/>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="122"/>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="137"/>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="155"/>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="162"/>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="163"/>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="164"/>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="165"/>
        <source>All</source>
        <translation type="unfinished">Все</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="105"/>
        <source>Check-In</source>
        <translation type="unfinished">Регистрация</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="106"/>
        <source>Check-Out</source>
        <translation type="unfinished">Выезд</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="107"/>
        <source>Appear</source>
        <translation type="unfinished">Появиться</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="115"/>
        <source>Group</source>
        <translation type="unfinished">Группа</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="130"/>
        <source>AI Type</source>
        <translation type="unfinished">Тип ИИ</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="138"/>
        <source>Human</source>
        <translation type="unfinished">Человек</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="139"/>
        <source>Vehicle</source>
        <translation type="unfinished">Транспортное средство</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="140"/>
        <source>Crowd</source>
        <translation type="unfinished">Толпа</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="148"/>
        <source>Camera</source>
        <translation type="unfinished">Камера</translation>
    </message>
</context>
<context>
    <name>FooterDialog</name>
    <message>
        <location filename="../common/widget/dialogs/base_dialog.py" line="251"/>
        <location filename="../common/widget/dialogs/base_dialog.py" line="254"/>
        <location filename="../common/widget/dialogs/base_dialog.py" line="257"/>
        <location filename="../common/widget/dialogs/base_dialog.py" line="263"/>
        <location filename="../common/widget/dialogs/base_dialog.py" line="266"/>
        <location filename="../common/widget/dialogs/base_dialog.py" line="269"/>
        <source>Cancel</source>
        <translation type="unfinished">Отмена</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/base_dialog.py" line="252"/>
        <location filename="../common/widget/dialogs/base_dialog.py" line="271"/>
        <source>Save</source>
        <translation type="unfinished">Сохранить</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/base_dialog.py" line="255"/>
        <source>Update</source>
        <translation type="unfinished">Обновить</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/base_dialog.py" line="258"/>
        <location filename="../common/widget/dialogs/base_dialog.py" line="261"/>
        <source>Create</source>
        <translation type="unfinished">Создать</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/base_dialog.py" line="260"/>
        <source>Close</source>
        <translation type="unfinished">Закрыть</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/base_dialog.py" line="264"/>
        <source>Connect</source>
        <translation type="unfinished">Подключить</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/base_dialog.py" line="267"/>
        <source>Ok</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>FooterInDialog</name>
    <message>
        <location filename="../common/widget/dialogs/footer_widget.py" line="29"/>
        <source>Save</source>
        <translation type="unfinished">Сохранить</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/footer_widget.py" line="31"/>
        <source>Cancel</source>
        <translation type="unfinished">Отмена</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/footer_widget.py" line="50"/>
        <location filename="../common/widget/dialogs/footer_widget.py" line="54"/>
        <source>Update</source>
        <translation type="unfinished">Обновить</translation>
    </message>
</context>
<context>
    <name>FrameModel</name>
    <message>
        <source>No Data</source>
        <translation type="obsolete">Нет данных</translation>
    </message>
    <message>
        <source>Clear Selection</source>
        <translation type="obsolete">Очистить выбор</translation>
    </message>
    <message>
        <source>Zoom to Selection</source>
        <translation type="obsolete">Приблизить к выбору</translation>
    </message>
    <message>
        <source>Export video</source>
        <translation type="obsolete">Экспортировать видео</translation>
    </message>
</context>
<context>
    <name>GeneralSettingTab</name>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="33"/>
        <source>1. Language:</source>
        <translation type="unfinished">1. Язык:</translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="40"/>
        <source>2. Personalize:</source>
        <translation type="unfinished">2. Персонализация:</translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="55"/>
        <source>Auto</source>
        <translation type="unfinished">Авто</translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="59"/>
        <source>Light Theme</source>
        <translation type="unfinished">Светлая тема</translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="63"/>
        <source>Dark Theme</source>
        <translation type="unfinished">Тёмная тема</translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="96"/>
        <source>English</source>
        <translation type="unfinished">Английский</translation>
    </message>
    <message>
        <source>Vietnamese</source>
        <translation type="obsolete">Вьетнамский</translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="114"/>
        <source>Russian</source>
        <translation type="unfinished">Русский</translation>
    </message>
</context>
<context>
    <name>GridItemContextMenuBase</name>
    <message>
        <location filename="../presentation/camera_screen/base/GridItemContextMenuBase.qml" line="71"/>
        <source>Delete %1 cameras</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/base/GridItemContextMenuBase.qml" line="75"/>
        <location filename="../presentation/camera_screen/base/GridItemContextMenuBase.qml" line="122"/>
        <source>Remove from view</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/base/GridItemContextMenuBase.qml" line="83"/>
        <source>Open camera to...</source>
        <translation type="unfinished">Открыть камеру для...</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/base/GridItemContextMenuBase.qml" line="89"/>
        <source>New screen</source>
        <translation type="unfinished">Новый экран</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/base/GridItemContextMenuBase.qml" line="97"/>
        <source>New saved screen</source>
        <translation type="unfinished">Новый сохранённый экран</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/base/GridItemContextMenuBase.qml" line="107"/>
        <source>New virtual window</source>
        <translation type="unfinished">Новое виртуальное окно</translation>
    </message>
</context>
<context>
    <name>GridItemContextMenuCamera</name>
    <message>
        <location filename="../presentation/camera_screen/handlers/GridItemContextMenuCamera.qml" line="31"/>
        <source>AI flow</source>
        <translation type="unfinished">Поток ИИ</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/handlers/GridItemContextMenuCamera.qml" line="37"/>
        <source>Recognition</source>
        <translation type="unfinished">Распознавание</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/handlers/GridItemContextMenuCamera.qml" line="46"/>
        <source>Protection</source>
        <translation type="unfinished">Защита</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/handlers/GridItemContextMenuCamera.qml" line="55"/>
        <source>Frequency</source>
        <translation type="unfinished">Частота</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/handlers/GridItemContextMenuCamera.qml" line="67"/>
        <source>Video Stream</source>
        <translation type="unfinished">Видеопоток</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/handlers/GridItemContextMenuCamera.qml" line="76"/>
        <source>Auto</source>
        <translation type="unfinished">Авто</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/handlers/GridItemContextMenuCamera.qml" line="89"/>
        <source>High (Main)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/handlers/GridItemContextMenuCamera.qml" line="102"/>
        <source>Low (Sub)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/handlers/GridItemContextMenuCamera.qml" line="140"/>
        <source>Settings</source>
        <translation type="unfinished">Настройки камеры</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/handlers/GridItemContextMenuCamera.qml" line="156"/>
        <source>Fullscreen</source>
        <translation type="unfinished">Полный экран</translation>
    </message>
</context>
<context>
    <name>GridModel</name>
    <message>
        <source>Failed to save the grid</source>
        <translation type="obsolete">Не удалось сохранить сетку</translation>
    </message>
</context>
<context>
    <name>GroupConfig</name>
    <message>
        <source>Camera (*)</source>
        <translation type="obsolete">Камера (*)</translation>
    </message>
</context>
<context>
    <name>GroupInfoDialog</name>
    <message>
        <source>Group Configuration</source>
        <translation type="obsolete">Настройка группы</translation>
    </message>
    <message>
        <source>Group Name (*)</source>
        <translation type="obsolete">Название группы (*)</translation>
    </message>
</context>
<context>
    <name>GroupSettingDialog</name>
    <message>
        <source>All</source>
        <translation type="obsolete">Все</translation>
    </message>
    <message>
        <source>Group Name</source>
        <translation type="obsolete">Имя группы</translation>
    </message>
    <message>
        <source>Sub Group</source>
        <translation type="obsolete">Подгруппа</translation>
    </message>
    <message>
        <source>Number of Cameras</source>
        <translation type="obsolete">Количество камер</translation>
    </message>
    <message>
        <source>Description</source>
        <translation type="obsolete">Описание</translation>
    </message>
    <message>
        <source>Action</source>
        <translation type="obsolete">Действие</translation>
    </message>
    <message>
        <source>Cancel</source>
        <translation type="obsolete">Отмена</translation>
    </message>
</context>
<context>
    <name>GroupTable</name>
    <message>
        <source>NO</source>
        <translation type="obsolete">ПОРЯДОК</translation>
    </message>
    <message>
        <source>DEVICE NAME</source>
        <translation type="obsolete">ИМЯ УСТРОЙСТВА</translation>
    </message>
    <message>
        <source>IP ADDRESS</source>
        <translation type="obsolete">IP-АДРЕС</translation>
    </message>
    <message>
        <source>MAC ADDRESS</source>
        <translation type="obsolete">MAC-АДРЕС</translation>
    </message>
    <message>
        <source>GROUP</source>
        <translation type="obsolete">ГРУППА</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="280"/>
        <source>No</source>
        <translation type="unfinished">Нет</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="281"/>
        <source>Device name</source>
        <translation type="unfinished">Имя Устройства</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="282"/>
        <source>IP address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="283"/>
        <source>Mac address</source>
        <translation type="unfinished">MAC-адрес</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="284"/>
        <source>Group</source>
        <translation type="unfinished">Группа</translation>
    </message>
</context>
<context>
    <name>ImageAdjWidget</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="107"/>
        <source>Brightness</source>
        <translation type="unfinished">Яркость</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="109"/>
        <source>Sharpness</source>
        <translation type="unfinished">Резкость</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="111"/>
        <source>Contrast</source>
        <translation type="unfinished">Контраст</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="113"/>
        <source>Saturation</source>
        <translation type="unfinished">Насыщенность</translation>
    </message>
</context>
<context>
    <name>ImageAvatarDialog</name>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="615"/>
        <source>AVATAR</source>
        <translation type="unfinished">АВАТАР</translation>
    </message>
</context>
<context>
    <name>ImageWidget</name>
    <message>
        <location filename="../common/widget/image_widget.py" line="132"/>
        <source>Loading...</source>
        <translation type="unfinished">Загрузка...</translation>
    </message>
</context>
<context>
    <name>InputCallbackWithMessage</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1797"/>
        <source>Show menu</source>
        <translation type="unfinished">Показать меню</translation>
    </message>
</context>
<context>
    <name>InputDialogSavedView</name>
    <message>
        <location filename="../presentation/camera_screen/components/InputDialogSavedView.qml" line="57"/>
        <source>Press screen name to save...</source>
        <translation type="unfinished">Нажмите на имя экрана, чтобы сохранить...</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/components/InputDialogSavedView.qml" line="93"/>
        <source>Create</source>
        <translation type="unfinished">Создать</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/components/InputDialogSavedView.qml" line="126"/>
        <source>Cancel</source>
        <translation type="unfinished">Отмена</translation>
    </message>
</context>
<context>
    <name>InputDialogVirtualWindow</name>
    <message>
        <location filename="../presentation/camera_screen/components/InputDialogVirtualWindow.qml" line="57"/>
        <source>Press virtual window name...</source>
        <translation type="unfinished">Нажмите на имя виртуального окна...</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/components/InputDialogVirtualWindow.qml" line="92"/>
        <source>Create</source>
        <translation type="unfinished">Создать</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/components/InputDialogVirtualWindow.qml" line="125"/>
        <source>Cancel</source>
        <translation type="unfinished">Отмена</translation>
    </message>
</context>
<context>
    <name>InputWithDataCallback</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1528"/>
        <source>Show menu</source>
        <translation type="unfinished">Показать меню</translation>
    </message>
</context>
<context>
    <name>InputWithRequireField</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="2092"/>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="2177"/>
        <source>Show password</source>
        <translation type="unfinished">Показать пароль</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="2173"/>
        <source>Hide password</source>
        <translation type="unfinished">Скрыть пароль</translation>
    </message>
</context>
<context>
    <name>InputWithTitle</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1675"/>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1695"/>
        <source>Show password</source>
        <translation type="unfinished">Показать пароль</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1679"/>
        <source>Show menu</source>
        <translation type="unfinished">Показать меню</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1691"/>
        <source>Hide password</source>
        <translation type="unfinished">Скрыть пароль</translation>
    </message>
</context>
<context>
    <name>ItemGridCustom</name>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/list_item_grid_custom.py" line="140"/>
        <source>Divisions</source>
        <translation type="unfinished">Подразделения</translation>
    </message>
</context>
<context>
    <name>ItemNotification</name>
    <message>
        <source>Face</source>
        <translation type="obsolete">Лицо</translation>
    </message>
    <message>
        <source>License plate</source>
        <translation type="obsolete">Номерной знак</translation>
    </message>
    <message>
        <source>Access control</source>
        <translation type="obsolete">Контроль доступа</translation>
    </message>
    <message>
        <source>Crowd</source>
        <translation type="obsolete">Толпа</translation>
    </message>
    <message>
        <source>Frequency</source>
        <translation type="obsolete">Частота</translation>
    </message>
</context>
<context>
    <name>LabelLineEdit</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1340"/>
        <source>Exp: 5, 13, 202,...</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ListenShowNotification</name>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="137"/>
        <source>UNKNOWN</source>
        <translation>НЕИЗВЕСТНО</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="138"/>
        <source>An internal server error occurred.</source>
        <translation>Произошла внутренняя ошибка сервера.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="139"/>
        <source>Camera not found.</source>
        <translation>Камера не найдена.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="140"/>
        <source>The camera is invalid.</source>
        <translation>Камера недействительна.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="141"/>
        <source>The camera name cannot be null.</source>
        <translation>Имя камеры не может быть пустым.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="142"/>
        <source>This camera name already exists.</source>
        <translation>Это имя камеры уже существует.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="143"/>
        <source>The camera URL cannot be null.</source>
        <translation>URL камеры не может быть пустым.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="144"/>
        <source>This camera URL already exists.</source>
        <translation>Этот URL камеры уже существует.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="145"/>
        <source>The camera mainstream URL is invalid.</source>
        <translation>Основной URL потока камеры недействителен.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="146"/>
        <source>The camera substream URL is invalid.</source>
        <translation>Дополнительный URL потока камеры недействителен.</translation>
    </message>
    <message>
        <source>The camera’s supported main resolution is invalid.</source>
        <translation type="vanished">Поддерживаемое основное разрешение камеры недействительно.</translation>
    </message>
    <message>
        <source>The camera’s supported main FPS is invalid.</source>
        <translation type="vanished">Поддерживаемая основная частота кадров камеры недействительна.</translation>
    </message>
    <message>
        <source>The camera’s supported sub resolution is invalid.</source>
        <translation type="vanished">Поддерживаемое дополнительное разрешение камеры недействительно.</translation>
    </message>
    <message>
        <source>The camera’s supported sub FPS is invalid.</source>
        <translation type="vanished">Поддерживаемая дополнительная частота кадров камеры недействительна.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="147"/>
        <source>The camera&apos;s supported main resolution is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="148"/>
        <source>The camera&apos;s supported main FPS is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="149"/>
        <source>The camera&apos;s supported sub resolution is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="150"/>
        <source>The camera&apos;s supported sub FPS is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="151"/>
        <source>The camera status cannot be null.</source>
        <translation>Статус камеры не может быть пустым.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="152"/>
        <source>The camera state cannot be null.</source>
        <translation>Состояние камеры не может быть пустым.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="153"/>
        <source>Camera group not found.</source>
        <translation>Группа камер не найдена.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="154"/>
        <source>The camera group is invalid.</source>
        <translation>Группа камер недействительна.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="155"/>
        <source>Invalid parent-child relationship in camera group.</source>
        <translation>Недействительная родительско-дочерняя связь в группе камер.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="156"/>
        <source>The camera group name cannot be null.</source>
        <translation>Имя группы камер не может быть пустым.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="157"/>
        <source>This camera group name already exists.</source>
        <translation>Это имя группы камер уже существует.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="158"/>
        <source>The camera group parent ID does not exist.</source>
        <translation>ID родительской группы камер не существует.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="159"/>
        <source>The camera group child ID does not exist.</source>
        <translation>ID дочерней группы камер не существует.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="160"/>
        <source>Event not found.</source>
        <translation>Событие не найдено.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="161"/>
        <source>The event is invalid.</source>
        <translation>Событие недействительно.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="162"/>
        <source>The event profile ID cannot be null.</source>
        <translation>ID профиля события не может быть пустым.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="163"/>
        <source>The event creation time cannot be null.</source>
        <translation>Время создания события не может быть пустым.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="164"/>
        <source>The event image URL cannot be null.</source>
        <translation>URL изображения события не может быть пустым.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="165"/>
        <source>The event video URL cannot be null.</source>
        <translation>URL видео события не может быть пустым.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="166"/>
        <source>Metadata cannot be null.</source>
        <translation>Метаданные не могут быть пустыми.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="167"/>
        <source>The metadata is invalid.</source>
        <translation>Метаданные недействительны.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="168"/>
        <source>Profile not found.</source>
        <translation>Профиль не найден.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="169"/>
        <source>The profile name cannot be null.</source>
        <translation>Имя профиля не может быть пустым.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="170"/>
        <source>The profile is invalid.</source>
        <translation>Профиль недействителен.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="171"/>
        <source>This profile name already exists.</source>
        <translation>Это имя профиля уже существует.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="172"/>
        <source>This profile UUID already exists.</source>
        <translation>Этот UUID профиля уже существует.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="173"/>
        <source>AI flow not found, please press button to create AI and set zone for this AI</source>
        <translation type="unfinished">Поток ИИ не найден, пожалуйста, нажмите кнопку для создания ИИ и установите зону для этого ИИ</translation>
    </message>
    <message>
        <source>AI flow not found.</source>
        <translation type="vanished">AI-поток не найден.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="174"/>
        <source>The AI flow name cannot be null.</source>
        <translation>Имя AI-потока не может быть пустым.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="175"/>
        <source>The AI flow type cannot be null.</source>
        <translation>Тип AI-потока не может быть пустым.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="176"/>
        <source>The AI flow apply field cannot be null.</source>
        <translation>Поле применения AI-потока не может быть пустым.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="177"/>
        <source>The AI flow is invalid.</source>
        <translation>AI-поток недействителен.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="178"/>
        <source>The date format is invalid.</source>
        <translation>Неверный формат даты.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="179"/>
        <source>The username cannot be null.</source>
        <translation>Имя пользователя не может быть пустым.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="180"/>
        <source>This username already exists.</source>
        <translation>Это имя пользователя уже существует.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="181"/>
        <source>The password cannot be null.</source>
        <translation>Пароль не может быть пустым.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="182"/>
        <source>The user is invalid.</source>
        <translation>Пользователь недействителен.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="183"/>
        <source>The token is invalid.</source>
        <translation>Токен недействителен.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="184"/>
        <source>User not found.</source>
        <translation>Пользователь не найден.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="185"/>
        <source>The password is invalid.</source>
        <translation>Неверный пароль.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="186"/>
        <source>API key not found.</source>
        <translation>Ключ API не найден.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="187"/>
        <source>The camera ID cannot be null.</source>
        <translation>ID камеры не может быть пустым.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="188"/>
        <source>The camera group ID cannot be null.</source>
        <translation>ID группы камер не может быть пустым.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="189"/>
        <source>AI flow is not applied, please set zone for this AI flow</source>
        <translation type="unfinished">Поток ИИ не применен, пожалуйста, установите зону для этого потока ИИ</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="192"/>
        <source>Unable to connect to server.</source>
        <translation>Не удается подключиться к серверу.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="54"/>
        <source>Add Camera Successfully Using RTSP</source>
        <translation>Камера успешно добавлена с использованием RTSP.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="75"/>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="77"/>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="79"/>
        <source>Successfully Added Camera List</source>
        <translation>Список камер успешно добавлен.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="81"/>
        <source>Add Camera Successfully Using Onvif</source>
        <translation>Камера успешно добавлена с использованием Onvif.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="55"/>
        <source>Update Camera to Server Successfully</source>
        <translation>Камера успешно обновлена на сервере.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="59"/>
        <source>Successfully deleted Camera Group on Server</source>
        <translation>Группа камер успешно удалена с сервера.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="57"/>
        <source>Added Camera Group to Server Successfully</source>
        <translation>Группа камер успешно добавлена на сервер.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="56"/>
        <source>Successfully deleted Camera on Server</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="58"/>
        <source>Update Camera Group to Server Successfully</source>
        <translation>Группа камер успешно обновлена на сервере.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="60"/>
        <source>Added AIFlow to Server Successfully</source>
        <translation>AIFlow успешно добавлен на сервер.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="61"/>
        <source>Update AIFlow to Server Successfully</source>
        <translation>AI успешно обновлен на сервере.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="62"/>
        <source>There is no AI problem</source>
        <translation>Необходимо настроить AI и нарисовать область перед включением.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="63"/>
        <source>Successfully deleted AIFlow on Server</source>
        <translation>AIFlow успешно удален с сервера.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="64"/>
        <source>Added ShortcutID to Server Successfully</source>
        <translation>ShortcutID успешно добавлен на сервер.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="65"/>
        <source>Update ShortcutID to Server Successfully</source>
        <translation>ShortcutID успешно обновлен на сервере.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="66"/>
        <source>Successfully deleted ShortcutID on Server</source>
        <translation>ShortcutID успешно удален с сервера.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="67"/>
        <source>Update Successfully</source>
        <translation>Успешно обновлено.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="71"/>
        <source>Successfully completed apply AI</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="94"/>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="126"/>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="129"/>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="132"/>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="194"/>
        <source>An error occurred</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="98"/>
        <source>No response received</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>LoadingDataDialog</name>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="570"/>
        <source>Connecting...</source>
        <translation type="unfinished">Подключение...</translation>
    </message>
</context>
<context>
    <name>LoginDialog</name>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="119"/>
        <source>Failed to load captcha. Please try again later.</source>
        <translation type="unfinished">Не удалось загрузить капчу. Пожалуйста, повторите попытку позже.</translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="127"/>
        <source>Unable to connect to captcha service. Please check your network connection.</source>
        <translation type="unfinished">Не удается подключиться к сервису капчи. Пожалуйста, проверьте подключение к сети.</translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="141"/>
        <source>Add Server</source>
        <translation type="unfinished">Добавить сервер</translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="144"/>
        <source>Server IP</source>
        <translation type="unfinished">IP-адрес сервера</translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="144"/>
        <source>Server IP: *************</source>
        <translation type="unfinished">IP-адрес сервера: *************</translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="146"/>
        <source>Server Port</source>
        <translation type="unfinished">Порт сервера</translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="146"/>
        <source>Enter server port</source>
        <translation type="unfinished">Введите порт сервера</translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="148"/>
        <source>Event Port</source>
        <translation type="unfinished">Порт событий</translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="148"/>
        <source>Enter event port</source>
        <translation type="unfinished">Введите порт событий</translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="150"/>
        <source>Username</source>
        <translation type="unfinished">Имя пользователя</translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="150"/>
        <source>Enter username</source>
        <translation type="unfinished">Введите имя пользователя</translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="152"/>
        <source>Password</source>
        <translation type="unfinished">Пароль</translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="152"/>
        <source>Enter password</source>
        <translation type="unfinished">Введите пароль</translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="166"/>
        <source>Show password</source>
        <translation type="unfinished">Показать пароль</translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="174"/>
        <source>Change</source>
        <translation type="unfinished">изменение</translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="176"/>
        <source>Login</source>
        <translation type="unfinished">Войти</translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="181"/>
        <source>Cancel</source>
        <translation type="unfinished">Отмена</translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="273"/>
        <source>Please enter a address Server.</source>
        <translation type="unfinished">Пожалуйста, введите адрес сервера.</translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="276"/>
        <source>Please enter a server port.</source>
        <translation type="unfinished">Пожалуйста, введите порт сервера.</translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="279"/>
        <source>Please enter a websocket port.</source>
        <translation type="unfinished">Пожалуйста, введите порт веб-сокета.</translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="282"/>
        <source>Please enter a username.</source>
        <translation type="unfinished">Пожалуйста, введите имя пользователя.</translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="285"/>
        <source>Please enter a password.</source>
        <translation type="unfinished">Пожалуйста, введите пароль.</translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="292"/>
        <location filename="../presentation/server_screen/login_dialog.py" line="340"/>
        <source>This Server already exists.</source>
        <translation type="unfinished">Этот сервер уже существует.</translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="335"/>
        <source>This Server is connected. Please disconnect and login again.</source>
        <translation type="unfinished">Этот сервер подключен. Пожалуйста, отключитесь и войдите снова.</translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="372"/>
        <source>Failed to connect to Server</source>
        <translation type="unfinished">Не удалось подключиться к серверу</translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="375"/>
        <source>This account is not allowed to log in to the system.</source>
        <translation type="unfinished">Эта учетная запись не имеет права входа в систему.</translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="378"/>
        <source>Username or password is incorrect!</source>
        <translation type="unfinished">Имя пользователя или пароль неверны!</translation>
    </message>
</context>
<context>
    <name>LoginForm</name>
    <message>
        <source>Username</source>
        <translation type="obsolete">Имя пользователя</translation>
    </message>
    <message>
        <source>Show password</source>
        <translation type="obsolete">Показать пароль</translation>
    </message>
    <message>
        <source>Password</source>
        <translation type="obsolete">Пароль</translation>
    </message>
    <message>
        <source>Hide password</source>
        <translation type="obsolete">Скрыть пароль</translation>
    </message>
    <message>
        <source>Please enter a username.</source>
        <translation type="obsolete">Пожалуйста, введите имя пользователя.</translation>
    </message>
    <message>
        <source>Please enter a password.</source>
        <translation type="obsolete">Пожалуйста, введите пароль.</translation>
    </message>
    <message>
        <source>Username or password is incorrect!</source>
        <translation type="obsolete">Имя пользователя или пароль неверны!</translation>
    </message>
</context>
<context>
    <name>MainScreen</name>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="67"/>
        <source>Processing...</source>
        <translation type="unfinished">Обработка...</translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="67"/>
        <source>Cancel</source>
        <translation type="unfinished">Отмена</translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="271"/>
        <source>Add Camera Successfully Using RTSP</source>
        <translation type="unfinished">Камера успешно добавлена с использованием RTSP</translation>
    </message>
    <message>
        <source>Successfully Added Camera List</source>
        <translation type="obsolete">Список камер успешно добавлен</translation>
    </message>
    <message>
        <source>Add Camera Successfully Using Onvif</source>
        <translation type="obsolete">Камера успешно добавлена с использованием Onvif</translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="274"/>
        <source>Update Camera to Server Successfully</source>
        <translation type="unfinished">Камера успешно обновлена на сервере</translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="277"/>
        <location filename="../presentation/main_screen/main_screen.py" line="286"/>
        <source>Successfully deleted Camera Group on Server</source>
        <translation type="unfinished">Группа камер успешно удалена с сервера</translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="280"/>
        <location filename="../presentation/main_screen/main_screen.py" line="302"/>
        <source>Added Camera Group to Server Successfully</source>
        <translation type="unfinished">Группа камер успешно добавлена на сервер</translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="283"/>
        <source>Update Camera Group to Server Successfully</source>
        <translation type="unfinished">Группа камер успешно обновлена на сервере</translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="289"/>
        <source>Update Successfully</source>
        <translation type="unfinished">Успешно обновлено</translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="305"/>
        <source>Failed to add Camera Group to Server</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="308"/>
        <source>Failed to update Camera Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="311"/>
        <source>Successfully Updated Map</source>
        <translation type="unfinished">Карта успешно обновлена</translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="314"/>
        <source>Update map fail</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="317"/>
        <source>Start edit map</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="320"/>
        <source>End edit map</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="324"/>
        <source>Please enable edit map</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="327"/>
        <source>Open camera success</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="330"/>
        <source>Open camera failed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="333"/>
        <source>Camera exist in map</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="336"/>
        <source>Saved successfully!</source>
        <translation type="unfinished">Сохранено успешно!</translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="339"/>
        <source>Failed to save</source>
        <translation type="unfinished">Не удалось сохранить</translation>
    </message>
    <message>
        <source>Floor saved!</source>
        <translation type="obsolete">Этаж успешно сохранён</translation>
    </message>
    <message>
        <source>Failed to save the floor.</source>
        <translation type="obsolete">Не удалось сохранить этаж.</translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="343"/>
        <source>The map data has been updated.</source>
        <translation type="unfinished">Данные карты были обновлены.</translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="347"/>
        <source>Video exported successfully</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="351"/>
        <source>Failed to export video</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="355"/>
        <source>Camera already have location</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>MainTreeViewWidget</name>
    <message>
        <source>Open in Tab</source>
        <translation type="obsolete">Открыть во вкладке</translation>
    </message>
    <message>
        <source>Edit</source>
        <translation type="obsolete">Редактировать</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2946"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3056"/>
        <source>Remove</source>
        <translation type="unfinished">Удалить</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="110"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3165"/>
        <source>Camera List</source>
        <translation type="unfinished">Список камер</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="113"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3169"/>
        <source>Virtual Window List</source>
        <translation type="unfinished">Список виртуальных окон</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="114"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3173"/>
        <source>Saved View List</source>
        <translation type="unfinished">писок сохранённых видов</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="115"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3177"/>
        <source>Map List</source>
        <translation type="unfinished">Список карт</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="406"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="458"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2203"/>
        <source>This screen already contains a virtual window named</source>
        <translation type="unfinished">На этом экране уже есть виртуальное окно с именем</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="408"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="460"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2205"/>
        <source>Do you want to replace this virtual window?</source>
        <translation type="unfinished">Вы хотите заменить это виртуальное окно?</translation>
    </message>
    <message>
        <source>Not Found
No results match</source>
        <translation type="obsolete">Не найдено
Нет совпадающих результатов</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1106"/>
        <source>No search results</source>
        <translation type="unfinished">Нет результатов поиска</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1167"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1169"/>
        <source>Virtual Window </source>
        <translation type="unfinished">Виртуальное окно </translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1181"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1183"/>
        <source>View </source>
        <translation type="unfinished">Вид </translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1318"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1333"/>
        <source>This Shortcut id already exists</source>
        <translation type="unfinished">Этот ярлык уже существует</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1336"/>
        <source>Please Enter Complete Information</source>
        <translation type="unfinished">Пожалуйста, введите полную информацию</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1741"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1839"/>
        <source>This Virtual Screen already exists</source>
        <translation type="unfinished">Этот виртуальный экран уже существует</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1797"/>
        <source>This saved view already exists</source>
        <translation type="unfinished">Этот сохранённый вид уже существует</translation>
    </message>
    <message>
        <source>Create</source>
        <translation type="obsolete">Создать</translation>
    </message>
    <message>
        <source>Please enter map name and choose an image</source>
        <translation type="obsolete">Пожалуйста, введите название карты и выберите изображение</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1891"/>
        <source>Delete Floor</source>
        <translation type="unfinished">Удалить этаж</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1891"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2029"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2388"/>
        <source>Confirm</source>
        <translation type="unfinished">Подтвердить</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1891"/>
        <source>Are you sure you want to delete this floor?</source>
        <translation type="unfinished">Вы уверены, что хотите удалить этот этаж?</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1900"/>
        <source>Delete floor successfully</source>
        <translation type="unfinished">Этаж успешно удалён</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1903"/>
        <source>Failed to delete the floor.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1934"/>
        <source>Create Floor</source>
        <translation type="unfinished">Создать этаж</translation>
    </message>
    <message>
        <source>Floor created successfully</source>
        <translation type="obsolete">Этаж успешно создан</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1966"/>
        <source>Create floor failed</source>
        <translation type="unfinished">Не удалось создать этаж</translation>
    </message>
    <message>
        <source>The server has edited the building </source>
        <translation type="obsolete">Сервер отредактировал здание </translation>
    </message>
    <message>
        <source>The server has deleted a building.</source>
        <translation type="obsolete">Сервер удалил здание.</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2029"/>
        <source>Delete Building</source>
        <translation type="unfinished">Удалить здание</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2029"/>
        <source>Are you sure you want to delete this building?</source>
        <translation type="unfinished">Вы уверены, что хотите удалить это здание?</translation>
    </message>
    <message>
        <source>Delete building successfully</source>
        <translation type="obsolete">Здание успешно удалено</translation>
    </message>
    <message>
        <source>Open in Virtual Window</source>
        <translation type="obsolete">Открыть в виртуальном окне</translation>
    </message>
    <message>
        <source>Exit All Camera</source>
        <translation type="obsolete">Выход из всех камер</translation>
    </message>
    <message>
        <source>Record</source>
        <translation type="obsolete">Запись</translation>
    </message>
    <message>
        <source>Choose position to stream on grid</source>
        <translation type="obsolete">Выберите позицию для потока на сетке</translation>
    </message>
    <message>
        <source>Playback</source>
        <translation type="obsolete">Воспроизведение</translation>
    </message>
    <message>
        <source>Video Stream</source>
        <translation type="obsolete">Видеопоток</translation>
    </message>
    <message>
        <source>Exit</source>
        <translation type="obsolete">Выход</translation>
    </message>
    <message>
        <source>Building created successfully</source>
        <translation type="obsolete">Здание успешно создано</translation>
    </message>
    <message>
        <source>EDIT GROUP</source>
        <translation type="obsolete">Редактировать группу</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2564"/>
        <source>Turn off AI</source>
        <translation type="unfinished">Выключить ИИ</translation>
    </message>
    <message>
        <source>Human intrusion</source>
        <translation type="obsolete">Вторжение</translation>
    </message>
    <message>
        <source>Human flow</source>
        <translation type="obsolete">Поток</translation>
    </message>
    <message>
        <source>Human access control</source>
        <translation type="obsolete">Контроль</translation>
    </message>
    <message>
        <source>Human weapon</source>
        <translation type="obsolete">Оружие</translation>
    </message>
    <message>
        <source>Human identification</source>
        <translation type="obsolete">Идентификация</translation>
    </message>
    <message>
        <source>Vehicle intrusion</source>
        <translation type="obsolete">Вторжение</translation>
    </message>
    <message>
        <source>Vehicle flow</source>
        <translation type="obsolete">Поток</translation>
    </message>
    <message>
        <source>Vehicle access control</source>
        <translation type="obsolete">Контроль</translation>
    </message>
    <message>
        <source>Vehicle identification</source>
        <translation type="obsolete">Идентификация</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2588"/>
        <source>Stream group in</source>
        <translation type="unfinished">Группа потоков в</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2590"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2725"/>
        <source>Delete	Del</source>
        <translation type="unfinished">Удалить	Del</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2593"/>
        <source>Remove from Group</source>
        <translation type="unfinished">Удалить из группы</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2595"/>
        <source>Rename	F2</source>
        <translation type="unfinished">Переименовать	F2</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2598"/>
        <source>AI flow</source>
        <translation type="unfinished">Поток ИИ</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2600"/>
        <source>Setting	Ctrl+I</source>
        <translation type="unfinished">Настройки	Ctrl+I</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2620"/>
        <source>Open camera to ...    </source>
        <translation type="unfinished">Открыть камеру для ...    </translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3082"/>
        <source>Open digital map</source>
        <translation type="unfinished">Открыть цифровую карту</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3084"/>
        <source>Edit Map</source>
        <translation type="unfinished">Редактировать карту</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3089"/>
        <source>Create Building</source>
        <translation type="unfinished">Создать здание</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3105"/>
        <source>Edit Building</source>
        <translation type="unfinished">Редактировать здание</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3109"/>
        <source>Create floor</source>
        <translation type="unfinished">Создать этаж</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3113"/>
        <source>Remove building</source>
        <translation type="unfinished">Удалить здание</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3127"/>
        <source>Open floor</source>
        <translation type="unfinished">Открыть этаж</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3129"/>
        <source>Edit floor</source>
        <translation type="unfinished">Редактировать этаж</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3133"/>
        <source>Remove floor</source>
        <translation type="unfinished">Удалить этаж</translation>
    </message>
    <message>
        <source>Add</source>
        <translation type="obsolete">Добавить</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3093"/>
        <source>Remove all</source>
        <translation type="unfinished">Удалить все</translation>
    </message>
    <message>
        <source>Edit digital map</source>
        <translation type="obsolete">Редактировать цифровую карту</translation>
    </message>
    <message>
        <source>Create map 2D</source>
        <translation type="obsolete">Создать 2D-карту</translation>
    </message>
    <message>
        <source>Remove all map</source>
        <translation type="obsolete">Удалить всю карту</translation>
    </message>
    <message>
        <source>Create group	Ctrl+G</source>
        <translation type="obsolete">Создать группу	Ctrl+G</translation>
    </message>
    <message>
        <source>Video streams</source>
        <translation type="obsolete">Видеопотоки</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1626"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1680"/>
        <source>Trường hợp này xử lý sau khi refactor code</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Another client has created a building.</source>
        <translation type="obsolete">Другой клиент создал здание.</translation>
    </message>
    <message>
        <source>The server has updated the building </source>
        <translation type="obsolete">Сервер отредактировал здание </translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1990"/>
        <source>Updated successfully.</source>
        <translation type="unfinished">Обновление прошло успешно.</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1993"/>
        <source>Failed to update the building.</source>
        <translation type="unfinished">Не удалось обновить здание.</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2041"/>
        <source>Building deleted successfully.</source>
        <translation type="unfinished">Здание успешно удалено.</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2046"/>
        <source>Failed to delete the building.</source>
        <translation type="unfinished">Не удалось удалить здание.</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1886"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1928"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1973"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2022"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2083"/>
        <source>The map data has been updated.</source>
        <translation type="unfinished">Данные карты были обновлены.</translation>
    </message>
    <message>
        <source>The server has created a building.</source>
        <translation type="obsolete">Другой клиент создал здание.</translation>
    </message>
    <message>
        <source>Floor added successfully.</source>
        <translation type="obsolete">Этаж успешно добавлен.</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1951"/>
        <source>Failed to add the floor.</source>
        <translation type="unfinished">Не удалось добавить этаж.</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2105"/>
        <source>Building added successfully.</source>
        <translation type="unfinished">Здание успешно добавлено.</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2108"/>
        <source>Failed to add the building.</source>
        <translation type="unfinished">Не удалось добавить здание.</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2388"/>
        <source>Delete Item</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2388"/>
        <source>Do you want to delete this item?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2401"/>
        <source>Delete item successfully</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2405"/>
        <source>Failed to delete item.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2444"/>
        <source>Edit group</source>
        <translation type="unfinished">Редактировать группу</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2565"/>
        <source>Recognition</source>
        <translation type="unfinished">Распознавание</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2566"/>
        <source>Protection</source>
        <translation type="unfinished">Защита</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2567"/>
        <source>Frequency</source>
        <translation type="unfinished">Частота</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2568"/>
        <source>Access</source>
        <translation type="unfinished">Доступ</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2569"/>
        <source>Motion</source>
        <translation type="unfinished">Движение</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2570"/>
        <source>Traffic</source>
        <translation type="unfinished">Поток</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2571"/>
        <source>Weapon</source>
        <translation type="unfinished">Оружие</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2572"/>
        <source>UFO</source>
        <translation type="unfinished">Внеземные объекты</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2573"/>
        <source>Fire</source>
        <translation type="unfinished">Пожар</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2622"/>
        <source>Add camera to group</source>
        <translation type="unfinished">Добавить камеру в группу</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2737"/>
        <source>Open cameras to ...</source>
        <translation type="unfinished">Открыть камеры для ...</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2754"/>
        <source>Add cameras to group</source>
        <translation type="unfinished">Добавить камеры в группу</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2784"/>
        <source>Exit Streaming Camera </source>
        <translation type="unfinished">Выйти из потока камеры </translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2794"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2953"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3063"/>
        <source>Shortcut ID</source>
        <translation type="unfinished">ID ярлыка</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2798"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2957"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3067"/>
        <source>Set Shortcut ID</source>
        <translation type="unfinished">Установить ID ярлыка</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2839"/>
        <source>Exit streaming group </source>
        <translation type="unfinished">Выйти из группы потоков </translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2856"/>
        <source>Add Virtual Window</source>
        <translation type="unfinished">Добавить виртуальное окно</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2860"/>
        <source>Remove All Virtual Window</source>
        <translation type="unfinished">Удалить все виртуальные окна</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2864"/>
        <source>Close All Virtual Window</source>
        <translation type="unfinished">Закрыть все виртуальные окна</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2881"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3005"/>
        <source>Open</source>
        <translation type="unfinished">Открыть</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2913"/>
        <source>Switch</source>
        <translation type="unfinished">Переключить</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2942"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3051"/>
        <source>Rename</source>
        <translation type="unfinished">Переименовать</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2965"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3045"/>
        <source>Close</source>
        <translation type="unfinished">Закрыть</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2975"/>
        <source>Add Saved View</source>
        <translation type="unfinished">Добавить сохранённый вид</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2979"/>
        <source>Open All</source>
        <translation type="unfinished">Открыть всё</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2983"/>
        <source>Remove All</source>
        <translation type="unfinished">Удалить все</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2987"/>
        <source>Close All</source>
        <translation type="unfinished">Закрыть все</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3009"/>
        <source>New Tab</source>
        <translation type="unfinished">Новая вкладка</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3014"/>
        <source>Create Virtual Window</source>
        <translation type="unfinished">Создать виртуальное окно</translation>
    </message>
    <message>
        <source>Add Map</source>
        <translation type="obsolete">Добавить карту</translation>
    </message>
    <message>
        <source>Remove All Map</source>
        <translation type="obsolete">Удалить все карты</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3142"/>
        <source>Create group</source>
        <translation type="unfinished">Создать группу</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3147"/>
        <source>Connect to third-party server</source>
        <translation type="unfinished">Подключиться к серверу третьей стороны</translation>
    </message>
</context>
<context>
    <name>Map2DWidgetItem</name>
    <message>
        <source>Floor saved successfully.</source>
        <translation type="obsolete">Этаж успешно сохранён</translation>
    </message>
    <message>
        <source>To drag an item onto the Map, you need to enter edit mode.</source>
        <translation type="obsolete">Чтобы перетащить элемент на карту, необходимо войти в режим редактирования карты.</translation>
    </message>
</context>
<context>
    <name>Map2DonGrid</name>
    <message>
        <source>Loading Image...</source>
        <translation type="obsolete">Загрузка изображения...</translation>
    </message>
</context>
<context>
    <name>MapGridWidgetItem</name>
    <message>
        <source>Please enlarge the grid cell or switch to a grid size smaller than 3x3.</source>
        <translation type="obsolete">Пожалуйста, увеличьте размер ячейки сетки или установите размер сетки меньше 3x3.</translation>
    </message>
    <message>
        <source>To drag an item onto the Map, you need to enter Map edit mode.</source>
        <translation type="obsolete">Чтобы перетащить элемент на карту, необходимо войти в режим редактирования карты.</translation>
    </message>
    <message>
        <source>To drag an item onto the Map, you need to enter edit mode.</source>
        <translation type="obsolete">Чтобы перетащить элемент на карту, необходимо войти в режим редактирования карты.</translation>
    </message>
    <message>
        <source>Map saved successfully.</source>
        <translation type="obsolete">Карта успешно сохранена.</translation>
    </message>
    <message>
        <source>Failed to save map.</source>
        <translation type="obsolete">Не удалось сохранить карту.</translation>
    </message>
</context>
<context>
    <name>MapItem</name>
    <message>
        <location filename="../common/qml/map/MapItem.qml" line="103"/>
        <source>Name:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/MapItem.qml" line="104"/>
        <source>Location:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/MapItem.qml" line="130"/>
        <source>Remove Camera from Map</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/MapItem.qml" line="130"/>
        <source>Remove Building from Map</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>MapScreen</name>
    <message>
        <source>Update</source>
        <translation type="obsolete">Обновить</translation>
    </message>
</context>
<context>
    <name>MultiDropDownDialog</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/multidropdown.py" line="323"/>
        <source>Close</source>
        <translation type="unfinished">Закрыть</translation>
    </message>
</context>
<context>
    <name>NewCustomTabWidget</name>
    <message>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="83"/>
        <source>New Tab</source>
        <translation type="unfinished">Новая вкладка</translation>
    </message>
    <message>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="86"/>
        <source>Open Tab</source>
        <translation type="unfinished">Открыть вкладку</translation>
    </message>
    <message>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="129"/>
        <source>Add to Virtual Window</source>
        <translation type="unfinished">Добавить в виртуальное окно</translation>
    </message>
    <message>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="135"/>
        <source>Add to Saved View</source>
        <translation type="unfinished">Добавить в сохранённый вид</translation>
    </message>
    <message>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="158"/>
        <source>Save as...</source>
        <translation type="unfinished">Сохранить как...</translation>
    </message>
    <message>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="165"/>
        <source>Virtual Window</source>
        <translation type="unfinished">Виртуальное окно</translation>
    </message>
    <message>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="167"/>
        <source>Saved View</source>
        <translation type="unfinished">Сохранённый вид</translation>
    </message>
    <message>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="256"/>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="258"/>
        <source>Virtual Window </source>
        <translation type="unfinished">Виртуальное окно </translation>
    </message>
    <message>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="270"/>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="272"/>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="284"/>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="286"/>
        <source>View </source>
        <translation type="unfinished">Вид </translation>
    </message>
</context>
<context>
    <name>NewTabWidget</name>
    <message>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="870"/>
        <source>Close Tab</source>
        <translation type="unfinished">Закрыть вкладку</translation>
    </message>
    <message>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="873"/>
        <source>Close All Tabs</source>
        <translation type="unfinished">Закрыть все вкладки</translation>
    </message>
</context>
<context>
    <name>NewVirtualWindowDialog</name>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3313"/>
        <source>Saved View Name</source>
        <translation type="unfinished">Имя сохранённого вида</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3316"/>
        <source>Virtual Window Name</source>
        <translation type="unfinished">Имя виртуального окна</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3324"/>
        <source>Create</source>
        <translation type="unfinished">Создать</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3326"/>
        <source>Add Saved View</source>
        <translation type="unfinished">Добавить сохранённый вид</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3328"/>
        <source>Add Virtual Window</source>
        <translation type="unfinished">Добавить виртуальное окно</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3331"/>
        <source>Edit Virtual Window</source>
        <translation type="unfinished">Редактировать виртуальное окно</translation>
    </message>
</context>
<context>
    <name>PTZ_Dropdow</name>
    <message>
        <location filename="../common/widget/ptz_widget.py" line="976"/>
        <location filename="../common/widget/ptz_widget.py" line="980"/>
        <source>Please select Camera.</source>
        <translation type="unfinished">Пожалуйста, выберите камеру.</translation>
    </message>
    <message>
        <location filename="../common/widget/ptz_widget.py" line="984"/>
        <source>This camera does not support PTZ.</source>
        <translation type="unfinished">Эта камера не поддерживает PTZ.</translation>
    </message>
</context>
<context>
    <name>PreviewItem</name>
    <message>
        <location filename="../common/qml/map/PreviewItem.qml" line="428"/>
        <source>No floor plan available</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>RecursiveTreeview</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="2757"/>
        <source>ALL</source>
        <translation type="unfinished">ВСЕ</translation>
    </message>
</context>
<context>
    <name>RoleInfoDialog</name>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="802"/>
        <source>ROLE INFORMATION</source>
        <translation type="unfinished">ИНФОРМАЦИЯ О ГРУППЕ</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="852"/>
        <source>System Permissions</source>
        <translation type="unfinished">Системные разрешения</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="854"/>
        <source>List Of Users</source>
        <translation type="unfinished">Список пользователей</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="894"/>
        <source>Please enter the group name.</source>
        <translation type="unfinished">Пожалуйста, введите название группы.</translation>
    </message>
</context>
<context>
    <name>ScheduleUI</name>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="27"/>
        <source>Recording</source>
        <translation type="unfinished">Запись</translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="111"/>
        <source>Record Always</source>
        <translation type="unfinished">Всегда записывать</translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="161"/>
        <source>Do Not Record</source>
        <translation type="unfinished">Не записывать</translation>
    </message>
    <message>
        <source>Copy Schedule to...</source>
        <translation type="obsolete">Копировать расписание в...</translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="207"/>
        <source>Copy Schedule to</source>
        <translation type="unfinished">Копировать расписание в</translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="249"/>
        <source>ALL</source>
        <translation type="unfinished">ВСЕ</translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="281"/>
        <source>Mon</source>
        <translation type="unfinished">Пн</translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="281"/>
        <source>Tue</source>
        <translation type="unfinished">Вт</translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="281"/>
        <source>Wed</source>
        <translation type="unfinished">Ср</translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="281"/>
        <source>Thu</source>
        <translation type="unfinished">Чт</translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="281"/>
        <source>Fri</source>
        <translation type="unfinished">Пт</translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="281"/>
        <source>Sat</source>
        <translation type="unfinished">Сб</translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="281"/>
        <source>Sun</source>
        <translation type="unfinished">Вс</translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="466"/>
        <source>Keep Archive for...</source>
        <translation type="unfinished">Хранить архив в течение...</translation>
    </message>
    <message>
        <source>Max</source>
        <translation type="obsolete">Максимум</translation>
    </message>
    <message>
        <source>Minutes</source>
        <translation type="obsolete">Минуты</translation>
    </message>
    <message>
        <source>Hours</source>
        <translation type="obsolete">Часы</translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="568"/>
        <source>Days</source>
        <translation type="unfinished">Дни</translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="568"/>
        <source>Months</source>
        <translation type="unfinished">Месяцы</translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="676"/>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="891"/>
        <source>Auto</source>
        <translation type="unfinished">Авто</translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="704"/>
        <source>Schedule Settings</source>
        <translation type="unfinished">Настройки расписания</translation>
    </message>
    <message>
        <source>Quality</source>
        <translation type="obsolete">Качество</translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="810"/>
        <source>Low</source>
        <translation type="unfinished">Низкое</translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="810"/>
        <source>Medium</source>
        <translation type="unfinished">Среднее</translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="810"/>
        <source>High</source>
        <translation type="unfinished">Высокий</translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="810"/>
        <source>Best</source>
        <translation type="unfinished">Лучший</translation>
    </message>
</context>
<context>
    <name>ScreenTable</name>
    <message>
        <location filename="../presentation/setting_screen/widget/tracking_setting_tab.py" line="226"/>
        <source>NO</source>
        <translation type="unfinished">ПОРЯДОК</translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/tracking_setting_tab.py" line="227"/>
        <source>SCREEN NAME</source>
        <translation type="unfinished">ИМЯ ЭКРАНА</translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/tracking_setting_tab.py" line="228"/>
        <source>SELECT</source>
        <translation type="unfinished">ВЫБРАТЬ</translation>
    </message>
</context>
<context>
    <name>Search</name>
    <message>
        <location filename="../common/qml/videoplayback/Search.qml" line="29"/>
        <source>Search...</source>
        <translation type="unfinished">Поиск...</translation>
    </message>
</context>
<context>
    <name>SearchBar</name>
    <message>
        <location filename="../common/widget/search_widget/search_bar.py" line="24"/>
        <source>Search</source>
        <translation type="unfinished">Поиск</translation>
    </message>
</context>
<context>
    <name>SearchWidget</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="358"/>
        <source>Search</source>
        <translation type="unfinished">Поиск</translation>
    </message>
</context>
<context>
    <name>SelectCamerasDialog</name>
    <message>
        <location filename="../common/qml/models/recording_schedule.py" line="699"/>
        <source>Select Cameras</source>
        <translation type="unfinished">Выбрать камеры</translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/SelectCamerasDialog.qml" line="81"/>
        <source>No search results</source>
        <translation type="unfinished">Нет результатов поиска</translation>
    </message>
</context>
<context>
    <name>SelectTabWidget</name>
    <message>
        <location filename="../common/qml/map/SelectTabWidget.qml" line="27"/>
        <source>Select tab to move</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/SelectTabWidget.qml" line="60"/>
        <source>Cancel</source>
        <translation type="unfinished">Отмена</translation>
    </message>
    <message>
        <location filename="../common/qml/map/SelectTabWidget.qml" line="76"/>
        <source>Move</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ServerItem</name>
    <message>
        <location filename="../presentation/server_screen/server_item.py" line="142"/>
        <location filename="../presentation/server_screen/server_item.py" line="209"/>
        <location filename="../presentation/server_screen/server_item.py" line="354"/>
        <source>Connect</source>
        <translation type="unfinished">Подключить</translation>
    </message>
    <message>
        <source>Server IP:</source>
        <translation type="obsolete">IP-адрес сервера:</translation>
    </message>
    <message>
        <source>Server Port:</source>
        <translation type="obsolete">Порт сервера:</translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/server_item.py" line="182"/>
        <location filename="../presentation/server_screen/server_item.py" line="352"/>
        <source>Disconnect</source>
        <translation type="unfinished">Отключиться</translation>
    </message>
    <message>
        <source>Failed to connect to Server</source>
        <translation type="obsolete">Не удалось подключиться к серверу</translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/server_item.py" line="303"/>
        <source>Không thể kết nối tới server!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/server_item.py" line="315"/>
        <source>User information</source>
        <translation type="unfinished">Информация о пользователе</translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/server_item.py" line="319"/>
        <source>Change password</source>
        <translation type="unfinished">Сменить пароль</translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/server_item.py" line="322"/>
        <source>Delete</source>
        <translation type="unfinished">Удалить</translation>
    </message>
</context>
<context>
    <name>SettingScreen</name>
    <message>
        <location filename="../presentation/setting_screen/setting_screen.py" line="31"/>
        <source>General</source>
        <translation type="unfinished">Общее</translation>
    </message>
    <message>
        <source>Auto Update</source>
        <translation type="obsolete">Автоматическое обновление</translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/setting_screen.py" line="33"/>
        <source>Tracking configuration</source>
        <translation type="unfinished">Настройка отслеживания</translation>
    </message>
</context>
<context>
    <name>ShortcutIDDialog</name>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3432"/>
        <source>Shortcut ID</source>
        <translation type="unfinished">ID ярлыка</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3441"/>
        <source>Add Shortcut ID</source>
        <translation type="unfinished">Добавить ID ярлыка</translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3443"/>
        <source>Edit Shortcut ID</source>
        <translation type="unfinished">Редактировать ID ярлыка</translation>
    </message>
</context>
<context>
    <name>SquareButton</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1108"/>
        <source>Edit</source>
        <translation type="unfinished">Редактировать</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1115"/>
        <source>Standard Window Division</source>
        <translation type="unfinished">Стандартное деление окна</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1116"/>
        <source>Custom Window Division</source>
        <translation type="unfinished">Пользовательское деление окна</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1209"/>
        <source>Recognition</source>
        <translation type="unfinished">Распознавание</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1211"/>
        <source>Protection</source>
        <translation type="unfinished">Защита</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1213"/>
        <source>Frequency</source>
        <translation type="unfinished">Частота</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1215"/>
        <source>Access</source>
        <translation type="unfinished">Доступ</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1217"/>
        <source>Motion</source>
        <translation type="unfinished">Движение</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1219"/>
        <source>Traffic</source>
        <translation type="unfinished">Поток</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1221"/>
        <source>Weapon</source>
        <translation type="unfinished">Оружие</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1223"/>
        <source>UFO</source>
        <translation type="unfinished">Внеземные объекты</translation>
    </message>
    <message>
        <source>Human intrusion</source>
        <translation type="obsolete">Вторжение</translation>
    </message>
    <message>
        <source>Human flow</source>
        <translation type="obsolete">Поток</translation>
    </message>
    <message>
        <source>Human access control</source>
        <translation type="obsolete">Контроль</translation>
    </message>
    <message>
        <source>Human weapon</source>
        <translation type="obsolete">Оружие</translation>
    </message>
    <message>
        <source>Human identification</source>
        <translation type="obsolete">Идентификация</translation>
    </message>
    <message>
        <source>Vehicle intrusion</source>
        <translation type="obsolete">Вторжение</translation>
    </message>
    <message>
        <source>Vehicle flow</source>
        <translation type="obsolete">Поток</translation>
    </message>
    <message>
        <source>Vehicle access control</source>
        <translation type="obsolete">Контроль</translation>
    </message>
    <message>
        <source>Vehicle identification</source>
        <translation type="obsolete">Идентификация</translation>
    </message>
</context>
<context>
    <name>SubMenuAIFlow</name>
    <message>
        <source>Human intrusion</source>
        <translation type="obsolete">Вторжение</translation>
    </message>
    <message>
        <source>Human flow</source>
        <translation type="obsolete">Поток</translation>
    </message>
    <message>
        <source>Human access control</source>
        <translation type="obsolete">Контроль</translation>
    </message>
    <message>
        <source>Human weapon</source>
        <translation type="obsolete">Оружие</translation>
    </message>
    <message>
        <source>Human identification</source>
        <translation type="obsolete">Идентификация</translation>
    </message>
    <message>
        <source>Vehicle intrusion</source>
        <translation type="obsolete">Вторжение</translation>
    </message>
    <message>
        <source>Vehicle flow</source>
        <translation type="obsolete">Поток</translation>
    </message>
    <message>
        <source>Vehicle access control</source>
        <translation type="obsolete">Контроль</translation>
    </message>
    <message>
        <source>Vehicle identification</source>
        <translation type="obsolete">Идентификация</translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="301"/>
        <source>Recognition</source>
        <translation type="unfinished">Распознавание</translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="303"/>
        <source>Protection</source>
        <translation type="unfinished">Защита</translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="305"/>
        <source>Frequency</source>
        <translation type="unfinished">Частота</translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="307"/>
        <source>Access</source>
        <translation type="unfinished">Доступ</translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="309"/>
        <source>Motion</source>
        <translation type="unfinished">Движение</translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="311"/>
        <source>Traffic</source>
        <translation type="unfinished">Поток</translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="313"/>
        <source>Weapon</source>
        <translation type="unfinished">Оружие</translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="315"/>
        <source>UFO</source>
        <translation type="unfinished">Внеземные объекты</translation>
    </message>
</context>
<context>
    <name>SubMenuCreateObject</name>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="689"/>
        <source>Camera</source>
        <translation type="unfinished">Камера</translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="694"/>
        <source>Building</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="699"/>
        <source>Floor</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>SubMenuOpenCameraInTab</name>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="559"/>
        <source>New View</source>
        <translation type="unfinished">Новый вид</translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="565"/>
        <source>New Saved View</source>
        <translation type="unfinished">Новый сохранённый вид</translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="571"/>
        <source>New Virtual Window</source>
        <translation type="unfinished">Новое виртуальное окно</translation>
    </message>
</context>
<context>
    <name>TabDeviceWidget</name>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="274"/>
        <source>Group</source>
        <translation type="unfinished">Группа</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="295"/>
        <source>Camera</source>
        <translation type="unfinished">Камера</translation>
    </message>
    <message>
        <source>Tracking Script</source>
        <translation type="obsolete">Скрипт отслеживания</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="227"/>
        <source>Device Group</source>
        <translation type="unfinished">Группа устройств</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="228"/>
        <source>Device</source>
        <translation type="unfinished">Устройство</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="257"/>
        <source>Add Camera</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="332"/>
        <source>Add Group</source>
        <translation type="unfinished">Добавить группу</translation>
    </message>
    <message>
        <source>ADD TRACING SCRIPT</source>
        <translation type="obsolete">ДОБАВИТЬ СКРИПТ ОТСЛЕЖИВАНИЯ</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="269"/>
        <location filename="../presentation/device_management_screen/device_screen.py" line="350"/>
        <source>Search</source>
        <translation type="unfinished">Поиск</translation>
    </message>
    <message>
        <source>Enter Tracking Script Name</source>
        <translation type="obsolete">Введите название скрипта отслеживания</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="297"/>
        <source>Door</source>
        <translation type="unfinished">дверь</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="525"/>
        <source>ADD CAMERA</source>
        <translation type="unfinished">ДОБАВИТЬ КАМЕРУ</translation>
    </message>
    <message>
        <source>ADD</source>
        <translation type="obsolete">ДОБАВИТЬ</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="270"/>
        <source>Enter Camera Name</source>
        <translation type="unfinished">Введите название камеры</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="278"/>
        <source>Status</source>
        <translation type="unfinished">Статус</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="279"/>
        <location filename="../presentation/device_management_screen/device_screen.py" line="284"/>
        <location filename="../presentation/device_management_screen/device_screen.py" line="294"/>
        <location filename="../presentation/device_management_screen/device_screen.py" line="356"/>
        <source>All</source>
        <translation type="unfinished">Все</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="279"/>
        <source>Online</source>
        <translation type="unfinished">Онлайн</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="279"/>
        <source>Offline</source>
        <translation type="unfinished">Офлайн</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="285"/>
        <location filename="../presentation/device_management_screen/device_screen.py" line="357"/>
        <source>Face</source>
        <translation type="unfinished">Лицо</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="286"/>
        <location filename="../presentation/device_management_screen/device_screen.py" line="358"/>
        <source>Vehicle</source>
        <translation type="unfinished">Транспортное средство</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="290"/>
        <location filename="../presentation/device_management_screen/device_screen.py" line="362"/>
        <source>AI</source>
        <translation type="unfinished">AI</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="296"/>
        <source>AIBox</source>
        <translation type="unfinished">AIBox</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="299"/>
        <source>Device Type</source>
        <translation type="unfinished">Тип устройства</translation>
    </message>
    <message>
        <source>ADD GROUP</source>
        <translation type="obsolete">ДОБАВИТЬ ГРУППУ</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="351"/>
        <source>Enter Group Name</source>
        <translation type="unfinished">Введите название группы</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="529"/>
        <source>ADD INTEGRATED DEVICE</source>
        <translation type="unfinished">ДОБАВИТЬ ИНТЕГРИРОВАННОЕ УСТРОЙСТВО</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="555"/>
        <source>This feature is under development</source>
        <translation type="unfinished">Эта функция находится в разработке</translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="576"/>
        <source>Successfully Downloaded File</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="578"/>
        <source>File Download Failed</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>TabWidget</name>
    <message>
        <source>Logout</source>
        <translation type="obsolete">Выйти</translation>
    </message>
    <message>
        <source>Change Server</source>
        <translation type="obsolete">Сменить сервер</translation>
    </message>
    <message>
        <source>Close Tab</source>
        <translation type="obsolete">Закрыть вкладку</translation>
    </message>
    <message>
        <source>Close All Tabs</source>
        <translation type="obsolete">Закрыть все вкладки</translation>
    </message>
</context>
<context>
    <name>TimePickerWidget</name>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="331"/>
        <source>Start time</source>
        <translation type="unfinished">Время начала</translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="333"/>
        <source>End time</source>
        <translation type="unfinished">Время окончания</translation>
    </message>
</context>
<context>
    <name>TrackingGroupTableView</name>
    <message>
        <source>NO</source>
        <translation type="obsolete">ПОРЯДОК</translation>
    </message>
    <message>
        <source>TRACKING GROUP NAME</source>
        <translation type="obsolete">Название группы отслеживания</translation>
    </message>
    <message>
        <source>NUMBER OF CAMERAS</source>
        <translation type="obsolete">Количество камер</translation>
    </message>
    <message>
        <source>ACTIONS</source>
        <translation type="obsolete">ДЕЙСТВИЯ</translation>
    </message>
    <message>
        <source>Total: 50</source>
        <translation type="obsolete">Всего: 50</translation>
    </message>
    <message>
        <source>Show records/page: </source>
        <translation type="obsolete">Показать записей на странице: </translation>
    </message>
    <message>
        <source>Total: </source>
        <translation type="obsolete">Всего: </translation>
    </message>
</context>
<context>
    <name>TrackingSettingTab</name>
    <message>
        <location filename="../presentation/setting_screen/widget/tracking_setting_tab.py" line="377"/>
        <source>Tracking configuration</source>
        <translation type="unfinished">Настройка отслеживания</translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/tracking_setting_tab.py" line="405"/>
        <source>Show tracking screen</source>
        <translation type="unfinished">Показать экран отслеживания</translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/tracking_setting_tab.py" line="408"/>
        <source>Yes</source>
        <translation type="unfinished">Да</translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/tracking_setting_tab.py" line="412"/>
        <source>No</source>
        <translation type="unfinished">Нет</translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/tracking_setting_tab.py" line="419"/>
        <source>Screen tracking list</source>
        <translation type="unfinished">Список экранов отслеживания</translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/tracking_setting_tab.py" line="435"/>
        <source>Save</source>
        <translation type="unfinished">Сохранить</translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/tracking_setting_tab.py" line="462"/>
        <source>Saved successfully.</source>
        <translation type="unfinished">Сохранено успешно.</translation>
    </message>
</context>
<context>
    <name>TreeMapWidget</name>
    <message>
        <source>Open in Tab</source>
        <translation type="obsolete">Открыть во вкладке</translation>
    </message>
    <message>
        <source>Remove</source>
        <translation type="obsolete">Удалить</translation>
    </message>
    <message>
        <source>Create</source>
        <translation type="obsolete">Создать</translation>
    </message>
    <message>
        <source>Please enter map name and choose an image</source>
        <translation type="obsolete">Пожалуйста, введите название карты и выберите изображение</translation>
    </message>
    <message>
        <source>Add Map</source>
        <translation type="obsolete">Добавить карту</translation>
    </message>
    <message>
        <source>Remove All Map</source>
        <translation type="obsolete">Удалить все карты</translation>
    </message>
    <message>
        <source>Edit</source>
        <translation type="obsolete">Редактировать</translation>
    </message>
    <message>
        <source>Open in Virtual Window</source>
        <translation type="obsolete">Открыть в виртуальном окне</translation>
    </message>
    <message>
        <source>Exit All Camera</source>
        <translation type="obsolete">Выход из всех камер</translation>
    </message>
    <message>
        <source>Record</source>
        <translation type="obsolete">Запись</translation>
    </message>
    <message>
        <source>Choose position to stream on grid</source>
        <translation type="obsolete">Выберите позицию для потока на сетке</translation>
    </message>
    <message>
        <source>Playback</source>
        <translation type="obsolete">Воспроизведение</translation>
    </message>
    <message>
        <source>Video Stream</source>
        <translation type="obsolete">Видеопоток</translation>
    </message>
    <message>
        <source>Exit</source>
        <translation type="obsolete">Выход</translation>
    </message>
</context>
<context>
    <name>TreeViewWidget</name>
    <message>
        <source>No results found</source>
        <translation type="obsolete">Результаты не найдены</translation>
    </message>
    <message>
        <location filename="../common/widget/tree_view_widget.py" line="333"/>
        <source>No search results</source>
        <translation type="unfinished">Нет результатов поиска</translation>
    </message>
    <message>
        <location filename="../common/widget/tree_view_widget.py" line="647"/>
        <location filename="../common/widget/tree_view_widget.py" line="704"/>
        <location filename="../common/widget/tree_view_widget.py" line="952"/>
        <location filename="../common/widget/tree_view_widget.py" line="1006"/>
        <source>All</source>
        <translation type="unfinished">Все</translation>
    </message>
    <message>
        <location filename="../common/widget/tree_view_widget.py" line="870"/>
        <source>Exit Streaming Camera </source>
        <translation type="unfinished">Выйти из потока камеры </translation>
    </message>
    <message>
        <location filename="../common/widget/tree_view_widget.py" line="874"/>
        <source>Choose position to stream on grid</source>
        <translation type="unfinished">Выберите позицию для потока на сетке</translation>
    </message>
    <message>
        <location filename="../common/widget/tree_view_widget.py" line="884"/>
        <source>Exit Streaming Group </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/tree_view_widget.py" line="891"/>
        <source>Edit</source>
        <translation type="unfinished">Редактировать</translation>
    </message>
    <message>
        <location filename="../common/widget/tree_view_widget.py" line="893"/>
        <source>Delete</source>
        <translation type="unfinished">Удалить</translation>
    </message>
</context>
<context>
    <name>UserGroupsTableView</name>
    <message>
        <source>NO</source>
        <translation type="obsolete">ПОРЯДОК</translation>
    </message>
    <message>
        <source>USER GROUP NAME</source>
        <translation type="obsolete">НАЗВАНИЕ ГРУППЫ ПОЛЬЗОВАТЕЛЕЙ</translation>
    </message>
    <message>
        <source>DESCRIPTION</source>
        <translation type="obsolete">ОПИСАНИЕ</translation>
    </message>
    <message>
        <source>STATUS</source>
        <translation type="obsolete">СТАТУС</translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="317"/>
        <source>ACTIONS</source>
        <translation type="unfinished">ДЕЙСТВИЯ</translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="123"/>
        <source>Total: 50</source>
        <translation type="unfinished">Всего: 50</translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="49"/>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="316"/>
        <source>No</source>
        <translation type="unfinished">Нет</translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="49"/>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="316"/>
        <source>User group name</source>
        <translation type="unfinished">Название группы пользователей</translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="49"/>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="316"/>
        <source>Description</source>
        <translation type="unfinished">Описание</translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="49"/>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="316"/>
        <source>Status</source>
        <translation type="unfinished">Статус</translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="50"/>
        <source>Actions</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="125"/>
        <source>Show records/page: </source>
        <translation type="unfinished">Показать записей на странице: </translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="214"/>
        <source>Total: </source>
        <translation type="unfinished">Всего: </translation>
    </message>
</context>
<context>
    <name>UserInformationDialog</name>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="329"/>
        <source>USER DETAIL</source>
        <translation type="unfinished">ДЕТАЛИ ПОЛЬЗОВАТЕЛЯ</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="351"/>
        <source>Avatar</source>
        <translation type="unfinished">Аватар</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="368"/>
        <source>Username</source>
        <translation type="unfinished">Имя пользователя</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="369"/>
        <source>Full Name</source>
        <translation type="unfinished">Полное имя</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="370"/>
        <source>System User Group</source>
        <translation type="unfinished">Группа пользователей системы</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="374"/>
        <source>Enter Username</source>
        <translation type="unfinished">Введите имя пользователя</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="377"/>
        <source>Enter Full Name</source>
        <translation type="unfinished">Введите полное имя</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="383"/>
        <source>Select Group</source>
        <translation type="unfinished">Выберите группу</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="398"/>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="400"/>
        <source>Subsystem</source>
        <translation type="unfinished">Подсистема</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="399"/>
        <source>Phone number</source>
        <translation type="unfinished">Номер телефона</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="400"/>
        <source>Status</source>
        <translation type="unfinished">Статус</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="405"/>
        <source>Active</source>
        <translation type="unfinished">Активный</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="405"/>
        <source>In-active</source>
        <translation type="unfinished">Неактивный</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="408"/>
        <source>Phone Number</source>
        <translation type="unfinished">Номер телефона</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="422"/>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="427"/>
        <source>Position</source>
        <translation type="unfinished">Должность</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="423"/>
        <source>Gender</source>
        <translation type="unfinished">Пол</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="424"/>
        <source>Email</source>
        <translation type="unfinished">Электронная почта</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="431"/>
        <source>Male</source>
        <translation type="unfinished">Мужской</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="431"/>
        <source>Female</source>
        <translation type="unfinished">Женский</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="493"/>
        <source>Please enter the full name.</source>
        <translation type="unfinished">Пожалуйста, введите полное имя.</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="496"/>
        <source>Please choose an user group.</source>
        <translation type="unfinished">Пожалуйста, выберите группу пользователей.</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="499"/>
        <source>Please choose a subsystem.</source>
        <translation type="unfinished">Пожалуйста, выберите подсистему.</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="502"/>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="560"/>
        <source>Email is existing.</source>
        <translation type="unfinished">Электронная почта уже существует.</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="509"/>
        <source>Please select an avatar image.</source>
        <translation type="unfinished">Пожалуйста, выберите изображение аватара.</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="539"/>
        <source>Please enter a valid email address.
 </source>
        <translation type="unfinished">Пожалуйста, введите действительный адрес электронной почты.</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="552"/>
        <source>Username is existing.</source>
        <translation type="unfinished">Имя пользователя уже существует.</translation>
    </message>
</context>
<context>
    <name>UserPermissionsWidget</name>
    <message>
        <location filename="../presentation/user_permissions_screen/user_permissions_screen.py" line="217"/>
        <source>Users Management</source>
        <translation type="unfinished">Управление пользователями</translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/user_permissions_screen.py" line="218"/>
        <source>User Groups Management</source>
        <translation type="unfinished">Управление группами пользователей</translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/user_permissions_screen.py" line="239"/>
        <location filename="../presentation/user_permissions_screen/user_permissions_screen.py" line="287"/>
        <source>Search</source>
        <translation type="unfinished">Поиск</translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/user_permissions_screen.py" line="262"/>
        <source>Add User</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/user_permissions_screen.py" line="288"/>
        <source>Search by name</source>
        <translation type="unfinished">Поиск по имени</translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/user_permissions_screen.py" line="306"/>
        <source>Add User Group</source>
        <translation type="unfinished">Добавить группу пользователей</translation>
    </message>
    <message>
        <source>Refresh</source>
        <translation type="obsolete">Обновить</translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/user_permissions_screen.py" line="240"/>
        <source>Search by name, email, phone number, group</source>
        <translation type="unfinished">Поиск по имени, электронной почте, номеру телефона, группе</translation>
    </message>
    <message>
        <source>Delete</source>
        <translation type="obsolete">Удалить</translation>
    </message>
    <message>
        <source>ADD</source>
        <translation type="obsolete">ДОБАВИТЬ</translation>
    </message>
</context>
<context>
    <name>UsersTableView</name>
    <message>
        <source>NO</source>
        <translation type="obsolete">ПОРЯДОК</translation>
    </message>
    <message>
        <source>USERNAME</source>
        <translation type="obsolete">ИМЯ ПОЛЬЗОВАТЕЛЯ</translation>
    </message>
    <message>
        <source>FULLNAME</source>
        <translation type="obsolete">ПОЛНОЕ ИМЯ</translation>
    </message>
    <message>
        <source>EMAIL</source>
        <translation type="obsolete">ЭЛЕКТРОННАЯ ПОЧТА</translation>
    </message>
    <message>
        <source>PHONE NUMBER</source>
        <translation type="obsolete">НОМЕР ТЕЛЕФОНА</translation>
    </message>
    <message>
        <source>GROUP</source>
        <translation type="obsolete">ГРУППА</translation>
    </message>
    <message>
        <source>STATUS</source>
        <translation type="obsolete">СТАТУС</translation>
    </message>
    <message>
        <source>ACTIONS</source>
        <translation type="obsolete">ДЕЙСТВИЯ</translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="52"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="388"/>
        <source>No</source>
        <translation type="unfinished">Нет</translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="52"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="388"/>
        <source>Username</source>
        <translation type="unfinished">Имя пользователя</translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="52"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="388"/>
        <source>Fullname</source>
        <translation type="unfinished">Полное имя</translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="52"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="388"/>
        <source>Email</source>
        <translation type="unfinished">Электронная почта</translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="52"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="388"/>
        <source>Phone number</source>
        <translation type="unfinished">Номер телефона</translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="52"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="388"/>
        <source>Group</source>
        <translation type="unfinished">Группа</translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="52"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="388"/>
        <source>Status</source>
        <translation type="unfinished">Статус</translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="53"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="389"/>
        <source>Actions</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="143"/>
        <source>Total: 50</source>
        <translation type="unfinished">Всего: 50</translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="145"/>
        <source>Show records/page: </source>
        <translation type="unfinished">Показать записей на странице: </translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="236"/>
        <source>Total: </source>
        <translation type="unfinished">Всего: </translation>
    </message>
</context>
<context>
    <name>VideoControlWidget</name>
    <message>
        <source>No camera selected</source>
        <translation type="obsolete">Нет выбранной камеры</translation>
    </message>
</context>
<context>
    <name>VideoModel</name>
    <message>
        <source>No Data</source>
        <translation type="obsolete">Нет данных</translation>
    </message>
    <message>
        <source>Clear Selection</source>
        <translation type="obsolete">Очистить выбор</translation>
    </message>
    <message>
        <source>Zoom to Selection</source>
        <translation type="obsolete">Приблизить к выбору</translation>
    </message>
    <message>
        <source>Export video</source>
        <translation type="obsolete">Экспортировать видео</translation>
    </message>
</context>
<context>
    <name>WarningDialog</name>
    <message>
        <location filename="../common/widget/dialogs/warning_dialog.py" line="28"/>
        <source>Are you sure you want to delete?</source>
        <translation type="unfinished">Вы уверены, что хотите удалить?</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/warning_dialog.py" line="53"/>
        <source>Notification</source>
        <translation type="unfinished">Уведомление</translation>
    </message>
</context>
<context>
    <name>WarningEventDialog</name>
    <message>
        <source>All</source>
        <translation type="obsolete">Все</translation>
    </message>
</context>
<context>
    <name>WidgetConfigListUsersRole</name>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_list_users.py" line="52"/>
        <source>Number of users: </source>
        <translation type="unfinished">Количество пользователей: </translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_list_users.py" line="53"/>
        <source>0</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_list_users.py" line="62"/>
        <source>Search by name, email, phone number</source>
        <translation type="unfinished">Поиск по имени, электронной почте, номеру телефона</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_list_users.py" line="91"/>
        <source>No Data</source>
        <translation type="unfinished">Нет данных</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_list_users.py" line="107"/>
        <source>FULLNAME</source>
        <translation type="unfinished">ПОЛНОЕ ИМЯ</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_list_users.py" line="107"/>
        <source>USER GROUP</source>
        <translation type="unfinished">ГРУППА ПОЛЬЗОВАТЕЛЕЙ</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_list_users.py" line="107"/>
        <source>EMAIL</source>
        <translation type="unfinished">ЭЛЕКТРОННАЯ ПОЧТА</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_list_users.py" line="108"/>
        <source>PHONE NUMBER</source>
        <translation type="unfinished">НОМЕР ТЕЛЕФОНА</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_list_users.py" line="110"/>
        <source>ACTIONS</source>
        <translation type="unfinished">ДЕЙСТВИЯ</translation>
    </message>
</context>
<context>
    <name>WidgetConfigPermissionRole</name>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_permissions.py" line="43"/>
        <source>User group name</source>
        <translation type="unfinished">Название группы пользователей</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_permissions.py" line="43"/>
        <source>Enter Group Name</source>
        <translation type="unfinished">Введите название группы</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_permissions.py" line="48"/>
        <source>Description</source>
        <translation type="unfinished">Описание</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_permissions.py" line="48"/>
        <source>Enter Description</source>
        <translation type="unfinished">Введите описание</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_permissions.py" line="53"/>
        <source>Status</source>
        <translation type="unfinished">Статус</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_permissions.py" line="54"/>
        <source>Active</source>
        <translation type="unfinished">Активный</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_permissions.py" line="54"/>
        <source>In-active</source>
        <translation type="unfinished">Неактивный</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_permissions.py" line="126"/>
        <source>System Permissions</source>
        <translation type="unfinished">Системные разрешения</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_permissions.py" line="130"/>
        <source>Image Management</source>
        <translation type="unfinished">Управление изображениями</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_permissions.py" line="134"/>
        <source>Case Management Group</source>
        <translation type="unfinished">Управление группами профилей</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_permissions.py" line="138"/>
        <source>Camera Management</source>
        <translation type="unfinished">Управление камерами</translation>
    </message>
</context>
<context>
    <name>WidgetStatus</name>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="522"/>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="565"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="598"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="641"/>
        <source>In-Active</source>
        <translation type="unfinished">Неактивный</translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="524"/>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="575"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="600"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="651"/>
        <source>Active</source>
        <translation type="unfinished">Активный</translation>
    </message>
</context>
</TS>
