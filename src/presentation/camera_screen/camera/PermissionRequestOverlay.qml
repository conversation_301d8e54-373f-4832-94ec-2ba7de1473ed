import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import models 1.0

Item {
    id: root
    property var itemData

    visible: itemData && itemData.needsPermission

    // Listen to theme changes
    Connections {
        target: gridModel
        function onThemeChanged() {
            // Force re-evaluation of theme colors
        }
    }

    // Simple centered layout without background
    ColumnLayout {
        anchors.centerIn: parent
        spacing: 16

        Text {
            text: qsTr("Grant permission to access camera")
            color: gridModel ? gridModel.get_color_theme_by_key("text_color_all_app") : "white"
            font.pixelSize: 16
            Layout.alignment: Qt.AlignHCenter
            horizontalAlignment: Text.AlignHCenter
        }

        Button {
            text: qsTr("Grant Permission")
            Layout.alignment: Qt.AlignHCenter

            background: Rectangle {
                color: {
                    var primaryColor = gridModel ? gridModel.get_color_theme_by_key("primary") : "#4CAF50"
                    if (parent.pressed) return Qt.darker(primaryColor, 1.2)
                    if (parent.hovered) return Qt.lighter(primaryColor, 1.1)
                    return primaryColor
                }
                radius: 6
            }

            contentItem: Text {
                text: parent.text
                color: gridModel ? gridModel.get_color_theme_by_key("text_same_bg") : "white"
                font.pixelSize: 12
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }

            onClicked: {
                if (root.itemData) {
                    root.itemData.grantPermission()
                }
            }
        }
    }
}
