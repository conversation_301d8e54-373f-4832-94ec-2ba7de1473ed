import os
import logging.config
import tempfile
from logging.handlers import TimedRotatingFileHandler
from datetime import datetime
import sys
import platform
from typing import List, Set, Optional

# Import Qt logging for QML support
try:
    from PySide6.QtCore import QLoggingCategory, qInstallMessageHandler, QtMsgType, QMessageLogContext
    QT_AVAILABLE = True
except ImportError:
    QT_AVAILABLE = False
    print("Warning: PySide6 not available, QML logging will be disabled")

# Import config for build type detection
try:
    from src.utils.config import Config
    CONFIG_AVAILABLE = True
except ImportError:
    CONFIG_AVAILABLE = False
    print("Warning: Config not available, using default debug mode")

# ============================================================================
# LOG CONTROL CONFIGURATION
# ============================================================================

# Global flag to enable/disable all logging
LOGGING_ENABLED = True

# Build type detection
IS_RELEASE_BUILD = CONFIG_AVAILABLE and getattr(Config, 'BUILD_RELEASE', False)
IS_DEBUG_BUILD = not IS_RELEASE_BUILD

# QML Logging configuration
QML_LOGGING_ENABLED = True
QML_LOGGING_CATEGORIES: Set[str] = set()  # Empty = all categories allowed

# Set of allowed files/modules for logging (when LOGGING_ENABLED is True)
# If empty, all files are allowed. If not empty, only specified files/modules can log
ALLOWED_LOG_FILES: Set[str] = set()

# Set of allowed folders for logging
# If empty, all folders are allowed. If not empty, only files in specified folders can log
ALLOWED_LOG_FOLDERS: Set[str] = set()

# Set of blocked logger names (third-party libraries)
BLOCKED_LOGGERS: Set[str] = {
    "urllib3", "urllib3.connectionpool", "requests", "requests.packages.urllib3",
    "PIL", "matplotlib", "asyncio", "websockets", "pyjoystick"
}

# Release build log level restrictions
RELEASE_ALLOWED_LEVELS = {logging.WARNING, logging.ERROR, logging.CRITICAL}

def disable_all_logs():
    """Tắt tất cả log trong toàn bộ ứng dụng"""
    global LOGGING_ENABLED
    LOGGING_ENABLED = False
    logging.getLogger().setLevel(logging.CRITICAL + 1)  # Disable all levels

def enable_all_logs():
    """Mở tất cả log trong toàn bộ ứng dụng"""
    global LOGGING_ENABLED
    LOGGING_ENABLED = True
    logging.getLogger().setLevel(logging.DEBUG)

def set_allowed_files(files: List[str]):
    """
    Thiết lập danh sách file được phép log
    Args:
        files: Danh sách tên file (không cần đường dẫn đầy đủ)
    """
    global ALLOWED_LOG_FILES
    ALLOWED_LOG_FILES = set(files)

def set_allowed_folders(folders: List[str]):
    """
    Thiết lập danh sách thư mục được phép log
    Args:
        folders: Danh sách tên thư mục hoặc đường dẫn tương đối
    """
    global ALLOWED_LOG_FOLDERS
    ALLOWED_LOG_FOLDERS = set(folders)

def add_allowed_file(filename: str):
    """Thêm một file vào danh sách được phép log"""
    global ALLOWED_LOG_FILES
    ALLOWED_LOG_FILES.add(filename)

def add_allowed_folder(folder: str):
    """Thêm một thư mục vào danh sách được phép log"""
    global ALLOWED_LOG_FOLDERS
    ALLOWED_LOG_FOLDERS.add(folder)

def remove_allowed_file(filename: str):
    """Xóa một file khỏi danh sách được phép log"""
    global ALLOWED_LOG_FILES
    ALLOWED_LOG_FILES.discard(filename)

def remove_allowed_folder(folder: str):
    """Xóa một thư mục khỏi danh sách được phép log"""
    global ALLOWED_LOG_FOLDERS
    ALLOWED_LOG_FOLDERS.discard(folder)

def clear_allowed_files():
    """Xóa tất cả file khỏi danh sách được phép log (cho phép tất cả file)"""
    global ALLOWED_LOG_FILES
    ALLOWED_LOG_FILES.clear()

def clear_allowed_folders():
    """Xóa tất cả thư mục khỏi danh sách được phép log (cho phép tất cả thư mục)"""
    global ALLOWED_LOG_FOLDERS
    ALLOWED_LOG_FOLDERS.clear()

# ============================================================================
# QML LOGGING FUNCTIONS
# ============================================================================

def enable_qml_logging():
    """Bật QML logging"""
    global QML_LOGGING_ENABLED
    QML_LOGGING_ENABLED = True

def disable_qml_logging():
    """Tắt QML logging"""
    global QML_LOGGING_ENABLED
    QML_LOGGING_ENABLED = False

def set_qml_logging_categories(categories: List[str]):
    """
    Thiết lập danh sách QML logging categories được phép
    Args:
        categories: Danh sách tên category (ví dụ: ['qml', 'qt.qml.binding'])
    """
    global QML_LOGGING_CATEGORIES
    QML_LOGGING_CATEGORIES = set(categories)

def add_qml_logging_category(category: str):
    """Thêm một QML logging category"""
    global QML_LOGGING_CATEGORIES
    QML_LOGGING_CATEGORIES.add(category)

def remove_qml_logging_category(category: str):
    """Xóa một QML logging category"""
    global QML_LOGGING_CATEGORIES
    QML_LOGGING_CATEGORIES.discard(category)

def clear_qml_logging_categories():
    """Xóa tất cả QML logging categories (cho phép tất cả)"""
    global QML_LOGGING_CATEGORIES
    QML_LOGGING_CATEGORIES.clear()

# ============================================================================
# BUILD TYPE FUNCTIONS
# ============================================================================

def is_release_build() -> bool:
    """Kiểm tra xem có phải release build hay không"""
    return IS_RELEASE_BUILD

def is_debug_build() -> bool:
    """Kiểm tra xem có phải debug build hay không"""
    return IS_DEBUG_BUILD

def get_build_type() -> str:
    """Lấy loại build hiện tại"""
    return "RELEASE" if IS_RELEASE_BUILD else "DEBUG"

def is_logging_allowed(filename: str, filepath: Optional[str] = None, level: int = logging.DEBUG) -> bool:
    """
    Kiểm tra xem file có được phép log hay không
    Args:
        filename: Tên file (ví dụ: 'main.py')
        filepath: Đường dẫn đầy đủ của file (tùy chọn)
        level: Log level (logging.DEBUG, logging.INFO, etc.)
    Returns:
        True nếu được phép log, False nếu không
    """
    if not LOGGING_ENABLED:
        return False

    # Kiểm tra release build restrictions
    if IS_RELEASE_BUILD and level not in RELEASE_ALLOWED_LEVELS:
        return False

    # Nếu không có giới hạn file và folder, cho phép tất cả
    if not ALLOWED_LOG_FILES and not ALLOWED_LOG_FOLDERS:
        return True



    # Kiểm tra file
    if ALLOWED_LOG_FILES and filename in ALLOWED_LOG_FILES:
        return True

    # Kiểm tra folder
    if ALLOWED_LOG_FOLDERS and filepath:
        for allowed_folder in ALLOWED_LOG_FOLDERS:
            if allowed_folder in filepath:
                return True

    # Nếu có giới hạn nhưng không khớp, không cho phép
    if ALLOWED_LOG_FILES or ALLOWED_LOG_FOLDERS:
        return False

    return True

# ============================================================================
# ANSI COLOR CODES
# ============================================================================

# ANSI color codes for terminal output
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

# Log levels with colors
LOG_LEVELS = {
    'DEBUG': Colors.BLUE,
    'INFO': Colors.GREEN,
    'WARNING': Colors.YELLOW,
    'ERROR': Colors.RED,
    'CRITICAL': Colors.RED + Colors.BOLD
}

def create_dir_if_not_exists(directory: str):
    print(f"Creating directory {directory} if it does not exist")
    if not os.path.exists(directory):
        os.makedirs(directory)

# Set up logging directory based on build type
if IS_RELEASE_BUILD:
    # Release: save to user data directory
    if platform.system() == "Windows":
        # Use %LOCALAPPDATA% on Windows
        user_data_dir = os.path.join(os.environ.get('LOCALAPPDATA', tempfile.gettempdir()), 'JSC Rosoboronexport', 'iVMS')
    else:
        # Use ~/.local/share on Linux/Mac
        user_data_dir = os.path.join(os.path.expanduser('~'), '.local', 'share', 'iVMS')

    # Create directory if it doesn't exist
    try:
        create_dir_if_not_exists(user_data_dir)
    except Exception as e:
        print(f"Warning: Could not create log directory {user_data_dir}: {e}")
        # Fallback to temp directory
        user_data_dir = tempfile.gettempdir()
        print(f"Fallback to temp directory: {user_data_dir}")
    logs_dir = user_data_dir
    log_file = os.path.join(logs_dir, "ivms_log.txt")
else:
    # Debug: save to project root
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    logs_dir = project_root
    log_file = os.path.join(logs_dir, "log.txt")

print(f"Build type: {'RELEASE' if IS_RELEASE_BUILD else 'DEBUG'}")
print(f"Log file: {log_file}")

# Remove existing log file if it exists to ensure fresh start
if os.path.exists(log_file):
    try:
        os.remove(log_file)
        print(f"Removed existing log file: {log_file}")
    except Exception as e:
        print(f"Warning: Could not remove existing log file: {e}")

class ColoredFormatter(logging.Formatter):
    def format(self, record):
        # Add color to log level
        if record.levelname in LOG_LEVELS:
            record.levelname = f"{LOG_LEVELS[record.levelname]}{record.levelname}{Colors.ENDC}"

        # Add thread ID
        record.thread_id = f"[Thread-{record.thread}]"

        # Add timestamp in milliseconds
        record.msecs = int(record.msecs)

        return super().format(record)



# Custom filter để kiểm soát log
class LogFilter(logging.Filter):
    def filter(self, record):
        # Kiểm tra third-party loggers trước
        if record.name in BLOCKED_LOGGERS:
            return record.levelno >= logging.WARNING

        # Lấy thông tin file từ record
        filename = os.path.basename(record.pathname) if hasattr(record, 'pathname') else "unknown"
        filepath = record.pathname if hasattr(record, 'pathname') else None

        # Kiểm tra xem có được phép log không
        return is_logging_allowed(filename, filepath, record.levelno)



# ============================================================================
# QML MESSAGE HANDLER
# ============================================================================

def qt_message_handler(msg_type, context, message):
    """
    Custom Qt message handler để xử lý log từ QML
    """
    if not QML_LOGGING_ENABLED:
        return

    # Kiểm tra category filtering
    if QML_LOGGING_CATEGORIES and context.category not in QML_LOGGING_CATEGORIES:
        return

    # Map Qt message types to Python logging levels
    level_mapping = {
        QtMsgType.QtDebugMsg: logging.DEBUG,
        QtMsgType.QtInfoMsg: logging.INFO,
        QtMsgType.QtWarningMsg: logging.WARNING,
        QtMsgType.QtCriticalMsg: logging.ERROR,
        QtMsgType.QtFatalMsg: logging.CRITICAL,
    }

    level = level_mapping.get(msg_type, logging.INFO)

    # Kiểm tra release build restrictions cho QML
    if IS_RELEASE_BUILD and level not in RELEASE_ALLOWED_LEVELS:
        return

    # Format QML log message
    qml_filename = os.path.basename(context.file) if context.file else "QML"
    formatted_message = f"[QML:{qml_filename}:{context.line}] {context.category}: {message}"

    # Log through Python logger
    logger.log(level, formatted_message)

# Install Qt message handler if Qt is available
if QT_AVAILABLE:
    qInstallMessageHandler(qt_message_handler)

# Determine log levels based on build type
if IS_RELEASE_BUILD:
    console_level = "WARNING"
    file_level = "WARNING"
    root_level = "WARNING"
else:
    console_level = "DEBUG"
    file_level = "DEBUG"
    root_level = "DEBUG"

# Logging configuration
logging_schema = {
    "version": 1,
    "formatters": {
        "standard": {
            "()": ColoredFormatter,
            "format": "%(asctime)s.%(msecs)03d - %(levelname)s - %(thread_id)s - %(filename)s:%(lineno)d - %(message)s",
            "datefmt": "%Y-%m-%d %H:%M:%S"
        },
        "detailed": {
            "()": ColoredFormatter,
            "format": "%(asctime)s.%(msecs)03d - %(levelname)s - %(thread_id)s - %(name)s - %(filename)s:%(lineno)d - %(funcName)s - %(message)s",
            "datefmt": "%Y-%m-%d %H:%M:%S"
        }
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "formatter": "standard",
            "level": console_level,  # Dynamic level based on build type
            "stream": sys.stdout,
        },
        "file": {
            "class": "logging.FileHandler",
            "formatter": "detailed",
            "level": file_level,  # Dynamic level based on build type
            "filename": log_file,
            "mode": "w",  # Overwrite file each time (create new log.txt)
            "encoding": "utf-8"
        }
    },
    "root": {
        "level": root_level,  # Dynamic level based on build type
        "handlers": ["console", "file"],
        "propagate": False
    }
}

# Try to configure logging with file handler, fallback to console only if failed
try:
    logging.config.dictConfig(logging_schema)
    print(f"Logging configured successfully with file: {log_file}")
except Exception as e:
    print(f"Warning: Could not configure file logging: {e}")
    print("Fallback to console-only logging")

    # Fallback configuration with console only
    fallback_schema = {
        "version": 1,
        "formatters": {
            "standard": {
                "()": ColoredFormatter,
                "format": "%(asctime)s.%(msecs)03d - %(levelname)s - %(thread_id)s - %(filename)s:%(lineno)d - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S"
            }
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "formatter": "standard",
                "level": console_level,
                "stream": sys.stdout,
            }
        },
        "root": {
            "level": root_level,
            "handlers": ["console"],
            "propagate": False
        }
    }

    try:
        logging.config.dictConfig(fallback_schema)
        print("Console-only logging configured successfully")
    except Exception as fallback_error:
        print(f"Critical: Could not configure any logging: {fallback_error}")
        # Use basic logging as last resort
        logging.basicConfig(level=logging.WARNING if IS_RELEASE_BUILD else logging.DEBUG)

# Get the root logger
logger = logging.getLogger()

# Thêm filter vào tất cả handlers
log_filter = LogFilter()
for handler in logger.handlers:
    handler.addFilter(log_filter)

# Tắt debug log của các thư viện bên thứ 3
third_party_loggers = [
    "urllib3",
    "urllib3.connectionpool",
    "requests",
    "requests.packages.urllib3",
    "PIL",  # Pillow
    "matplotlib",
    "asyncio",
    "websockets",
    "pyjoystick"
]

for logger_name in third_party_loggers:
    logging.getLogger(logger_name).setLevel(logging.WARNING)

# ============================================================================
# CONVENIENCE FUNCTIONS
# ============================================================================

def configure_logging(
    enabled: bool = True,
    allowed_files: Optional[List[str]] = None,
    allowed_folders: Optional[List[str]] = None,
    qml_enabled: bool = True,
    qml_categories: Optional[List[str]] = None
):
    """
    Cấu hình logging một cách thuận tiện
    Args:
        enabled: Bật/tắt tất cả log
        allowed_files: Danh sách file được phép log (None = tất cả)
        allowed_folders: Danh sách thư mục được phép log (None = tất cả)
        qml_enabled: Bật/tắt QML logging
        qml_categories: Danh sách QML categories được phép (None = tất cả)
    """
    global LOGGING_ENABLED, ALLOWED_LOG_FILES, ALLOWED_LOG_FOLDERS
    global QML_LOGGING_ENABLED, QML_LOGGING_CATEGORIES

    LOGGING_ENABLED = enabled
    QML_LOGGING_ENABLED = qml_enabled

    if allowed_files is not None:
        ALLOWED_LOG_FILES = set(allowed_files)
    else:
        ALLOWED_LOG_FILES.clear()

    if allowed_folders is not None:
        ALLOWED_LOG_FOLDERS = set(allowed_folders)
    else:
        ALLOWED_LOG_FOLDERS.clear()

    if qml_categories is not None:
        QML_LOGGING_CATEGORIES = set(qml_categories)
    else:
        QML_LOGGING_CATEGORIES.clear()

    # Update logger level based on build type and enabled state
    if enabled:
        level = logging.WARNING if IS_RELEASE_BUILD else logging.DEBUG
        logging.getLogger().setLevel(level)
    else:
        logging.getLogger().setLevel(logging.CRITICAL + 1)

def get_logging_status() -> dict:
    """
    Lấy trạng thái hiện tại của hệ thống logging
    Returns:
        Dictionary chứa thông tin trạng thái
    """
    return {
        "enabled": LOGGING_ENABLED,
        "build_type": get_build_type(),
        "is_release_build": IS_RELEASE_BUILD,
        "allowed_files": list(ALLOWED_LOG_FILES),
        "allowed_folders": list(ALLOWED_LOG_FOLDERS),
        "total_allowed_files": len(ALLOWED_LOG_FILES),
        "total_allowed_folders": len(ALLOWED_LOG_FOLDERS),
        "qml_enabled": QML_LOGGING_ENABLED,
        "qml_categories": list(QML_LOGGING_CATEGORIES),
        "total_qml_categories": len(QML_LOGGING_CATEGORIES),
        "qt_available": QT_AVAILABLE,
        "config_available": CONFIG_AVAILABLE,
        "release_allowed_levels": [logging.getLevelName(level) for level in RELEASE_ALLOWED_LEVELS]
    }

def print_logging_status():
    """In ra trạng thái hiện tại của hệ thống logging"""
    status = get_logging_status()
    print(f"\n{Colors.BOLD}{Colors.BLUE}=== LOGGING STATUS ==={Colors.ENDC}")
    print(f"{Colors.GREEN}Enabled:{Colors.ENDC} {status['enabled']}")
    print(f"{Colors.GREEN}Build Type:{Colors.ENDC} {status['build_type']}")
    print(f"{Colors.GREEN}Release Build:{Colors.ENDC} {status['is_release_build']}")
    if status['is_release_build']:
        print(f"{Colors.YELLOW}Release Allowed Levels:{Colors.ENDC} {status['release_allowed_levels']}")
    print(f"{Colors.GREEN}Allowed files ({status['total_allowed_files']}):{Colors.ENDC} {status['allowed_files']}")
    print(f"{Colors.GREEN}Allowed folders ({status['total_allowed_folders']}):{Colors.ENDC} {status['allowed_folders']}")
    print(f"{Colors.GREEN}QML Enabled:{Colors.ENDC} {status['qml_enabled']}")
    print(f"{Colors.GREEN}QML Categories ({status['total_qml_categories']}):{Colors.ENDC} {status['qml_categories']}")
    print(f"{Colors.GREEN}Qt Available:{Colors.ENDC} {status['qt_available']}")
    print(f"{Colors.GREEN}Config Available:{Colors.ENDC} {status['config_available']}")
    print(f"{Colors.BLUE}{'='*35}{Colors.ENDC}\n")

# ============================================================================
# EXAMPLE USAGE FUNCTIONS
# ============================================================================

def demo_log_control():
    """Demo các tính năng điều khiển log"""
    print(f"\n{Colors.BOLD}{Colors.YELLOW}=== LOG CONTROL DEMO ==={Colors.ENDC}")

    # Show current status
    print_logging_status()

    # Test 1: Log bình thường
    print(f"\n{Colors.BLUE}1. Log bình thường:{Colors.ENDC}")
    logger.info("Test log message 1")
    logger.warning("Test warning message 1")
    logger.error("Test error message 1")

    # Test 2: Tắt tất cả log
    print(f"\n{Colors.BLUE}2. Tắt tất cả log:{Colors.ENDC}")
    disable_all_logs()
    logger.info("Test log message 2 (should not appear)")
    logger.warning("Test warning message 2 (should not appear)")

    # Test 3: Mở lại và chỉ cho phép file cụ thể
    print(f"\n{Colors.BLUE}3. Chỉ cho phép log_utils.py:{Colors.ENDC}")
    enable_all_logs()
    set_allowed_files(["log_utils.py"])
    logger.info("Test log message 3 (should appear)")

    # Test 4: Test QML logging
    print(f"\n{Colors.BLUE}4. Test QML logging:{Colors.ENDC}")
    if QT_AVAILABLE:
        print("QML logging is available")
        set_qml_logging_categories(["qml", "qt.qml.binding"])
    else:
        print("QML logging not available (PySide6 not imported)")

    # Test 5: Reset về mặc định
    print(f"\n{Colors.BLUE}5. Reset về mặc định:{Colors.ENDC}")
    clear_allowed_files()
    clear_qml_logging_categories()
    logger.info("Test log message 5 (should appear)")

    print(f"\n{Colors.YELLOW}{'='*40}{Colors.ENDC}\n")

def log_app_init(app_name, version, platform_info=None):
    """
    Log application initialization information with enhanced formatting
    
    Args:
        app_name: Name of the application
        version: Version of the application
        platform_info: Optional dictionary with platform information
    """
    if platform_info is None:
        platform_info = {
            "os": platform.system(),
            "os_version": platform.version(),
            "python_version": platform.python_version(),
            "machine": platform.machine(),
            "processor": platform.processor()
        }
    
    # Create a visually appealing header
    header = f"{Colors.BOLD}{Colors.BLUE}{'='*20} {app_name} v{version} Initialization {'='*20}{Colors.ENDC}"
    logger.info(header)
    
    # Log system information with colors
    logger.info(f"{Colors.GREEN}Start time:{Colors.ENDC} {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"{Colors.GREEN}OS:{Colors.ENDC} {platform_info['os']} {platform_info['os_version']}")
    logger.info(f"{Colors.GREEN}Python:{Colors.ENDC} {platform_info['python_version']}")
    logger.info(f"{Colors.GREEN}Machine:{Colors.ENDC} {platform_info['machine']}")
    logger.info(f"{Colors.GREEN}Processor:{Colors.ENDC} {platform_info['processor']}")
    logger.info(f"{Colors.GREEN}Log file:{Colors.ENDC} {log_file}")
    
    # Footer
    logger.info(f"{Colors.BOLD}{Colors.BLUE}{'='*60}{Colors.ENDC}")

def log_exception(exc_type, exc_value, exc_traceback):
    """Enhanced exception logging with color coding"""
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    
    # Log the exception with color coding
    logger.critical(f"{Colors.RED}Unhandled exception:{Colors.ENDC}", exc_info=(exc_type, exc_value, exc_traceback))

# Set the exception hook to log unhandled exceptions
sys.excepthook = log_exception

# Example usage of different log levels
def log_examples():
    logger.debug("This is a debug message")
    logger.info("This is an info message")
    logger.warning("This is a warning message")
    logger.error("This is an error message")
    logger.critical("This is a critical message")

# ============================================================================
# USAGE EXAMPLES
# ============================================================================

"""
Ví dụ sử dụng:

=== PYTHON LOGGING ===
1. Tắt tất cả log:
   disable_all_logs()

2. Mở tất cả log:
   enable_all_logs()

3. Chỉ cho phép log từ file cụ thể:
   set_allowed_files(["main.py", "database.py"])

4. Chỉ cho phép log từ thư mục cụ thể:
   set_allowed_folders(["src/models", "src/controllers"])

=== QML LOGGING ===
5. Tắt/mở QML logging:
   disable_qml_logging()
   enable_qml_logging()

6. Chỉ cho phép QML categories cụ thể:
   set_qml_logging_categories(["qml", "qt.qml.binding", "qt.qml.connections"])

7. Thêm/xóa QML category:
   add_qml_logging_category("qt.qml.models")
   remove_qml_logging_category("qt.qml.binding")

=== BUILD TYPE ===
8. Kiểm tra build type:
   print(f"Build type: {get_build_type()}")
   print(f"Is release: {is_release_build()}")

=== CẤU HÌNH NHANH ===
9. Cấu hình toàn diện:
   configure_logging(
       enabled=True,
       allowed_files=["main.py"],
       allowed_folders=["src/utils"],
       qml_enabled=True,
       qml_categories=["qml", "qt.qml.binding"]
   )

10. Kiểm tra trạng thái:
    print_logging_status()

=== RELEASE BUILD ===
- Trong release build, chỉ WARNING/ERROR/CRITICAL được hiển thị
- DEBUG và INFO bị tắt tự động
- QML logging cũng tuân theo quy tắc này

=== QML USAGE ===
Trong file QML, sử dụng:
- console.log("message")     -> INFO level
- console.warn("message")    -> WARNING level
- console.error("message")   -> ERROR level
"""
# Cấu hình logging cho các file cụ thể - TẠM THỜI CHO PHÉP TẤT CẢ ĐỂ DEBUG
# Cấu hình mặc định cho các file cần debug
# set_allowed_files(["frame_model.py", "live_stream_player.py", "pyav_wrapper.py", "video_player_manager.py", "message_processor.py"])

# Uncomment để test logging
# log_examples()
# demo_log_control()
