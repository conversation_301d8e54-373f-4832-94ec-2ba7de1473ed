
#create

from dataclasses import dataclass, asdict, fields
import json
import datetime
from typing import List
from PySide6.QtCore import QObject, Property, Signal
from src.common.model.base_model import BaseModel
from PySide6.QtGui import QPixmap
import logging
logger = logging.getLogger(__name__)

@dataclass
class EventAI:
    id: str = None
    createdAt:str = None
    event: str = None
    imageUrl: str = None
    imageCropUrl: str = None
    imageFullUrl: str = None
    imageLicensePlateUrl: str = None
    name: str = None
    type: str = None
    status: str = None
    ioId: List[int] = None
    ioGroups: List[int] = None
    warningConfig: List[int] = None
    cameraLocation: str = None
    cameraId: str = None
    cameraName: str = None
    pixmapFullUrl: QPixmap = None
    pixmapUrl: QPixmap = None
    pixmapLicensePlateUrl: QPixmap = None
    cameraLatitude: str = None
    cameraLongitude: str = None
    createdAtLocalDate: str = None
    
    @classmethod
    def from_dict(cls, data_dict):
        field_names = {field.name for field in fields(cls)}
        filtered_dict = {key: value for key, value in data_dict.items() if key in field_names}
        return cls(**filtered_dict)

    def to_dict(self):
        # only return the fields that are not None
        dist = {}
        for k, v in self.__dict__.items():
           if k != 'pixmapFullUrl' and k != 'pixmapUrl' and k != 'pixmapLicensePlateUrl' and v is not None:
               dist[k] = v
        return dist
        # return {k: v for k, v in self.__dict__.items() if v is not None and v != QPixmap}
class EventModel(QObject):
    def __init__(self,data: dict):
        super().__init__()
        print(f'EventModel = {data}')
        self.data = data

    def get_property(self, key, default=None):
        """Lấy giá trị từ self.data, hỗ trợ key lồng nhau dạng 'a.b.c'."""
        if '.' in key:
            keys = key.split('.')
            value = self.data
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            return value
        return self.data.get(key, default)

    def set_property(self, key, value):
        """Cập nhật giá trị trong self.data."""
        self.data[key] = value

    @Property(str, constant=True)
    def id(self):
        return self.get_property("id",None)

    @Property(str, constant=True)
    def name(self):
        return self.get_property("name",None)

    @Property(str, constant=True)
    def aiType(self):
        return self.get_property("type",None)

    @Property(str, constant=True)
    def imageFullUrl(self):
        return self.get_property("imageFullUrl",None)

    @Property(str, constant=True)
    def imageUrl(self):
        return self.get_property("imageUrl",None)

    @Property(str, constant=True)
    def status(self):
        return self.get_property("status",None)
    
    @Property(str, constant=True)
    def cameraName(self):
        return self.get_property("cameraName",None)
    
    @Property(str, constant=True)
    def createdAtLocalDate(self):
        event_time = ""
        try:
            # Handle ISO format datetime with timezone
            event_time = datetime.datetime.fromisoformat(self.get_property("createdAtLocalDate",None)).strftime("%Y-%m-%d %H:%M:%S")
        except ValueError:
            try:
                # Fallback to parsing without timezone
                event_time = datetime.datetime.strptime(self.get_property("createdAtLocalDate",None), "%Y-%m-%d %H:%M:%S.%f").strftime("%Y-%m-%d %H:%M:%S")
            except ValueError:
                # If all parsing fails, use current time
                event_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        return event_time
@dataclass
class Data(BaseModel):
    content: List[EventAI] = None
    totalElements: int = None
    def __post_init__(self):
        # print(f'self.content = {self.content}')
        if self.content is not None:
            self.content = [EventAI.from_dict(event_ai) for event_ai in self.content]

@dataclass
class EventData(BaseModel):
    code: str = None
    message: str = None
    data: Data = None
    def __post_init__(self):
        if self.data:
            self.data = Data.from_dict(self.data)


@dataclass
class CameraModel(BaseModel):
    id: int = None
    camera_name: str = None
    camera_url: str = None
    camera_monitoring: bool = None
    camera_created_at: str = None
    camera_updated_at: str = None
    camera_ai_type: str = None
    camera_location: int = None
    metadata: 'MetadataCameraModel' = None
    restream_address: str = None
    restream_port: int = None
    exclude_profiles: str = None
    time_range: str = None
    times_between_event: str = None
    event_num: int = None
    profiles: str = None
    crowd_methods: str = None
    warning: bool = None
    warning_type: str = None
    device: 'Device' = None
    camera_ip: str = None

    def __post_init__(self):
        if self.metadata:
            # convert str to dict
            self.metadata = json.loads(self.metadata)
            # convert it to MetadataCameraModel
            self.metadata = MetadataCameraModel.from_dict(self.metadata)
        if self.device:
            self.device = json.loads(self.device)
            self.device = Device.from_dict(self.device)
    

@dataclass
class Profile(BaseModel):
    id: int = None
    users: List['User'] = None
    vehicles: List = None
    name: str = None
    description: str = None
    warning: bool = None

    def __post_init__(self):
        self.users = [User.from_dict(user) for user in self.users]

@dataclass
class EventAIItemDisplay(BaseModel):
    time: str = None
    image: str = None
    ai_type: str = None
    camera_name: str = None


@dataclass
class MetadataTraffic(BaseModel):
    x: float = None
    y: float = None
    w: float = None
    h: float = None
    vehicle_brand: str = None
    brand_conf: float = None
    vehicle_conf: float = None
    color_conf: float = None
    license_conf: float = None
    vehicle_name: str = None
    vehicle_type: str = None
    vehicle_color: str = None
    license_plate: str = None
    license_plate_x: float = None
    license_plate_y: float = None
    license_plate_w: float = None
    license_plate_h: float = None
    direction_type: str = None



@dataclass
class MetadataHuman(BaseModel):
    face_name: str = None
    distance: float = None
    bounding_box: list = None
    kpss: list = None
    is_add_faiss_local: bool = None




@dataclass
class MetadataCrowed(BaseModel):
    count: int = None
    x: int = None
    y: int = None
    w: int = None
    h: int = None
    crop_list: List['CropItemObjectCrowd'] = None

    def __post_init__(self):
        # convert self.crop_list to dict
        # self.crop_list = json.loads(self.crop_list)
        if self.crop_list is None:
            self.crop_list = []
        else:
            crop_list_temp : dict = self.crop_list
            self.crop_list = []
            # parser crop_list item to CropItemObjectCrowd [1] and [2]
            for key, value in crop_list_temp.items():
                self.crop_list.append(CropItemObjectCrowd(key, value))

@dataclass
class CropItemObjectCrowd(BaseModel):
    id: str = None
    image: str = None


@dataclass
class MetadataAccessControl(MetadataTraffic):
    check_in_type: str = None
    cameraCheckInApply: bool = None
    cameraCheckOutApply: bool = None
    warningLevel1: int = None
    warningLevel2: int = None
    warningLevel3: int = None
    x1: float = None
    x2: float = None
    x3: float = None
    x4: float = None
    y1: float = None
    y2: float = None
    y3: float = None
    y4: float = None
    crowdMethods: str = None
    warningContext: str = None


@dataclass
class MetadataCameraModel(BaseModel):
    cameraCheckInApply: bool = None
    cameraCheckOutApply: bool = None
    warningLevel1: int = None
    warningLevel2: int = None
    warningLevel3: int = None
    x1: float = None
    x2: float = None
    x3: float = None
    x4: float = None
    y1: float = None
    y2: float = None
    y3: float = None
    y4: float = None
    crowdMethods: str = None
    warningContext: str = None
    polygons: str = None
    


@dataclass
class User(BaseModel):
    id: int = None
    soCmt: str = None
    hoVaTen: str = None
    namSinh: str = None
    queQuan: str = None
    noiTru: str = None
    dacDiemNhanDang: str = None
    ngayCap: str = None
    noiCap: str = None
    quocTich: str = None
    ngayHetHan: str = None
    gioiTinh: str = None
    status: str = None
    image: str = None
    vehicle: str = None
    auxCmts: list = None
    position: str = None
    department: str = None
    email: str = None
    phone: str = None
    

@dataclass
class Device(BaseModel):
    id: int = None
    name: str = None
    mac: str = None

class EventManager(QObject):
    add_event_data = Signal(dict)
    add_event_list_signal = Signal(str)
    add_event_signal = Signal(str)
    # delete_camera_model_signal = Signal(list)
    # add_camera_signal = Signal(QObject)
    # add_cameras_signal = Signal(list)
    __instance = None
    def __init__(self):
        super().__init__()
        self.add_event_data.connect(self.add_event)
        self.event_list = {}
        self.event_page_list = None

    @staticmethod
    def get_instance():
        if EventManager.__instance is None:
            EventManager.__instance = EventManager()
        return EventManager.__instance
    
    def register_signal(self,widget = None):
        if hasattr(widget,'add_event_list_signal'):
            self.add_event_list_signal.connect(widget.add_event_list_signal)
        if hasattr(widget,'add_event_signal'):
            self.add_event_signal.connect(widget.add_event_signal)

    def unregister_signal(self,widget = None):
        if hasattr(widget,'add_event_list_signal'):
            self.add_event_list_signal.disconnect(widget.add_event_list_signal)
        if hasattr(widget,'add_event_signal'):
            self.add_event_signal.disconnect(widget.add_event_signal)

    def add_event_list(self,event_list:EventData = None):
        # self.event_list = event_list
        self.event_page_list = event_list
        self.add_event_list_signal.emit('add_event_list')

    def add_event(self,data = None):
        logger.debug(f'event = {data}')
        try:
            event = EventModel(data = data)
            event.set_property("imageFullUrl",event.get_property("imageUrl"))
            event.set_property("imageUrl",event.get_property("imageCropUrl"))

            data = self.event_list.get('websocket_data',None)
            if data is None:
                origin = EventData()
                origin.data = Data()
                origin.data.content = [event]
                self.event_list['websocket_data'] = origin
            else:
                data.data.content.append(event)
            self.add_event_signal.emit(event.id)
        except Exception as e:
            print(f'add_event websocket error = {e}')

    def get_event_list(self):
        return self.event_list
    
    def get_event(self,id = None):
        for page_index, event_list in self.event_list.items():
            for event in event_list.data.content:
                if event.id == id:
                    return event

event_manager = EventManager.get_instance()
