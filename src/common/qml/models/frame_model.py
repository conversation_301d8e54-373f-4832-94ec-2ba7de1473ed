from PySide6.QtQuick import QQuickPaintedItem, QQuickItem
from PySide6.QtCore import Property, Signal, Slot, Qt, QEvent, QTimer,QObject
from PySide6.QtWidgets import QMenu
from PySide6.QtGui import Q<PERSON><PERSON>ter, QPixmap,QCursor
from PySide6.QtQml import QJ<PERSON><PERSON>ue
from typing import Callable, List
from src.styles.style import Style, Theme
from src.common.camera.video_player_manager import video_player_manager
from src.common.camera.player import CameraState, Player
from src.common.controller.controller_manager import controller_manager,Controller
from src.common.model.camera_model import camera_model_manager,CameraModel
from src.common.controller.main_controller import main_controller
from src.common.qml.models.timelinecontroller import TimeLineController,SpeedStatus
from src.common.qml.models.common_enum import CommonEnum
from src.common.model.record_model import record_model_manager, RecordData
from src.presentation.camera_screen.export_video_dialog import ExportVideoDialog
from src.common.qml.models.export_video_data import ExportVideoData
from src.common.threads.sub_thread import SubThread
from queue import Queue
import time
import uuid
import logging
import threading
logger = logging.getLogger(__name__)


class FrameModel(QQuickPaintedItem):
    clicked = Signal()
    doubleClicked = Signal()
    rightClicked = Signal()
    frameCountChanged = Signal(int)
    cameraStateChanged = Signal(str)
    # actuallyVisibleChanged = Signal(bool)
    itemDataChanged = Signal()
    contentBoundsChanged = Signal(float, float, float, float)  # x, y, width, height
    cameraRestartRequested = Signal(str)  # Signal to request camera restart

    def __init__(self, parent=None):
        super().__init__(parent)
        self.uuid = uuid.uuid4()
        self._model = None
        self._q_image = None
        self.player = None
        self._grid_row = -1
        self._grid_col = -1
        self._is_playing = False
        self._itemData = None
        self._isSelected = False
        self._frame_count = 0

        self._is_actually_visible = False

        # Track previous content bounds to only emit signal on changes
        self._prev_content_bounds_x = 0.0
        self._prev_content_bounds_y = 0.0
        self._prev_content_bounds_width = 0.0
        self._prev_content_bounds_height = 0.0
        self.is_loading_timeline_ui = False
        # Disable mouse events - let GridItemActionHandler handle them
        self.setAcceptedMouseButtons(Qt.NoButton)
        self.setAcceptHoverEvents(False)

    def paint(self, painter: QPainter):
        if self._q_image is None:
            # When no frame available, emit contentBounds = full size
            # This ensures borders and overlays resize correctly during grid zoom
            target_rect = self.boundingRect()
            full_width = int(target_rect.width())
            full_height = int(target_rect.height())

            # Only emit if bounds have changed to avoid unnecessary signals
            if (self._prev_content_bounds_x != 0 or
                self._prev_content_bounds_y != 0 or
                self._prev_content_bounds_width != full_width or
                self._prev_content_bounds_height != full_height):

                self._prev_content_bounds_x = 0
                self._prev_content_bounds_y = 0
                self._prev_content_bounds_width = full_width
                self._prev_content_bounds_height = full_height

                self.contentBoundsChanged.emit(0, 0, full_width, full_height)
            return

        target_rect = self.boundingRect()
        container_width = target_rect.width()
        container_height = target_rect.height()

        frame_width = self._q_image.width()
        frame_height = self._q_image.height()

        if frame_width <= 0 or frame_height <= 0:
            return

        # Calculate aspect ratios
        frame_aspect = frame_width / frame_height
        container_aspect = container_width / container_height

        # Calculate scale factor and content dimensions (similar to CustomImage logic)
        if frame_aspect > container_aspect:
            # Frame is wider - fit to container width
            scale_factor = container_width / frame_width
            content_width = container_width
            content_height = frame_height * scale_factor
            x_offset = 0
            y_offset = (container_height - content_height) / 2
        else:
            # Frame is taller - fit to container height
            scale_factor = container_height / frame_height
            content_width = frame_width * scale_factor
            content_height = container_height
            x_offset = (container_width - content_width) / 2
            y_offset = 0

        # Save painter state
        painter.save()

        # Enable anti-aliasing for smoother rendering
        painter.setRenderHint(QPainter.Antialiasing, True)
        painter.setRenderHint(QPainter.SmoothPixmapTransform, True)

        # Apply transform: translate to center position, then scale
        painter.translate(x_offset, y_offset)
        painter.scale(scale_factor, scale_factor)

        # Draw original pixmap at (0,0) - painter transform will handle scaling and positioning
        painter.drawPixmap(0, 0, self._q_image)

        # Restore painter state
        painter.restore()

        # Calculate content bounds for signal emission (using ceil for consistency with QML)
        import math
        new_x = int(math.floor(x_offset))
        new_y = int(math.floor(y_offset))
        new_width = int(math.ceil(content_width))
        new_height = int(math.ceil(content_height))

        # Only emit signal if content bounds have changed
        if (self._prev_content_bounds_x != new_x or
            self._prev_content_bounds_y != new_y or
            self._prev_content_bounds_width != new_width or
            self._prev_content_bounds_height != new_height):

            # Update previous values
            self._prev_content_bounds_x = new_x
            self._prev_content_bounds_y = new_y
            self._prev_content_bounds_width = new_width
            self._prev_content_bounds_height = new_height

            # Emit signal for QML to listen and update GridItemBase properties
            self.contentBoundsChanged.emit(new_x, new_y, new_width, new_height)

    @Slot(QPixmap)
    def updateFrame(self, frame):
        if frame is not None:
            self._q_image = frame
            self._frame_count += 1
            self.frameCountChanged.emit(self._frame_count)

            # Không gọi self.update() khi đang animation để tránh repaint không cần thiết
            if self._itemData and hasattr(self._itemData, 'isAnimating') and self._itemData.isAnimating:
                # Skip update during animation to avoid unnecessary repaints
                return

            self.update()

    def mousePressEvent(self, event):
        if event.button() == Qt.RightButton:
            self.rightClicked.emit()
        else:
            self.clicked.emit()

    def mouseDoubleClickEvent(self, event):
        self.doubleClicked.emit()

    @Property(int)
    def gridRow(self):
        return self._grid_row

    @gridRow.setter
    def gridRow(self, row):
        self._grid_row = row

    @Property(int)
    def gridCol(self):
        return self._grid_col

    @gridCol.setter
    def gridCol(self, col):
        self._grid_col = col

    @Property("QVariant")
    def model(self):
        return self._model

    @model.setter
    def model(self, value):
        if self._model != value:
            self._model = value

    @Property(int, notify=frameCountChanged)
    def frameCount(self):
        return self._frame_count


    @Property(QObject, notify=itemDataChanged)
    def itemData(self):
        return self._itemData

    @itemData.setter
    def itemData(self, value):
        logger.debug(f"[FrameModel] Item data changed: {value}")
        if self._itemData != value:
            self._itemData = value
            if self._itemData:
                width = int(self.width())
                height = int(self.height())
                if width <= 0 or height <= 0:
                    # Use default size if component size is invalid
                    width = 1920
                    height = 1080
                if self._itemData.player:
                    self._itemData.player.update_resize(width=width, height=height, uuid=self._itemData.uuid)

                if self not in self._itemData.frameModels:
                    self._itemData.frameModels.append(self)
            self.itemDataChanged.emit()
        
    @Property(bool)
    def isSelected(self):
        return self._isSelected

    @isSelected.setter
    def isSelected(self, value:bool):
        if self._isSelected != value:
            self._isSelected = value

